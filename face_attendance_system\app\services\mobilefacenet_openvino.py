"""
MobileFaceNet OpenVINO Module
Provides fast face embedding extraction using MobileFaceNet ONNX model via OpenVINO
"""

import cv2
import numpy as np
import logging
import time
import os
from pathlib import Path
from typing import List, Optional, Tuple
import urllib.request

try:
    import openvino as ov
    OPENVINO_AVAILABLE = True
except ImportError:
    OPENVINO_AVAILABLE = False
    logging.warning("OpenVINO not available, falling back to ONNX Runtime")

logger = logging.getLogger(__name__)

class MobileFaceNetOpenVINO:
    """
    MobileFaceNet face embedding extraction using OpenVINO
    Optimized for CPU inference with high performance
    """
    
    def __init__(self, 
                 model_path: Optional[str] = None,
                 input_size: Tuple[int, int] = (112, 112)):
        """
        Initialize MobileFaceNet with OpenVINO
        
        Args:
            model_path: Path to MobileFaceNet ONNX model
            input_size: Input image size (width, height)
        """
        self.input_size = input_size
        self.model_path = model_path
        
        # Performance tracking
        self.inference_times = []
        self.total_inferences = 0
        
        # OpenVINO components
        self.core = None
        self.compiled_model = None
        self.input_layer = None
        self.output_layer = None
        
        # Initialize the model
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize OpenVINO model"""
        if not OPENVINO_AVAILABLE:
            raise Exception("OpenVINO is not available")
        
        try:
            # Download model if not provided
            if self.model_path is None:
                self.model_path = self._download_mobilefacenet_model()
            
            # Check if model exists
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"Model not found: {self.model_path}")
            
            logger.info(f"🔄 Loading MobileFaceNet model: {self.model_path}")
            
            # Initialize OpenVINO Core
            self.core = ov.Core()
            
            # Read the model
            model = self.core.read_model(self.model_path)
            
            # Compile model for CPU with optimizations
            # Use compatible configuration for different OpenVINO versions
            try:
                self.compiled_model = self.core.compile_model(
                    model,
                    "CPU",
                    {
                        "PERFORMANCE_HINT": "LATENCY",
                        "INFERENCE_PRECISION_HINT": "f32"
                    }
                )
            except Exception as config_error:
                logger.warning(f"⚠️ Advanced config failed: {config_error}")
                # Fallback to basic compilation
                self.compiled_model = self.core.compile_model(model, "CPU")
            
            # Get input and output layers
            self.input_layer = self.compiled_model.input(0)
            self.output_layer = self.compiled_model.output(0)
            
            logger.info(f"✅ MobileFaceNet OpenVINO model loaded successfully")
            logger.info(f"   Input shape: {self.input_layer.shape}")
            logger.info(f"   Output shape: {self.output_layer.shape}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize MobileFaceNet OpenVINO: {e}")
            raise
    
    def _download_mobilefacenet_model(self) -> str:
        """Download MobileFaceNet ONNX model if not available"""
        models_dir = Path("app/models")
        models_dir.mkdir(exist_ok=True)

        model_path = models_dir / "mobilefacenet.onnx"

        if model_path.exists():
            logger.info(f"✅ MobileFaceNet model already exists: {model_path}")
            return str(model_path)

        # Try multiple model sources for MobileFaceNet
        model_urls = [
            # Lightweight MobileFaceNet model (preferred)
            "https://github.com/deepinsight/insightface/releases/download/v0.7/buffalo_l.zip",
            # Alternative: Use a smaller ArcFace model as fallback
            "https://github.com/onnx/models/raw/main/vision/body_analysis/arcface/model/arcface-resnet100-8.onnx"
        ]

        for i, model_url in enumerate(model_urls):
            try:
                logger.info(f"🔄 Downloading MobileFaceNet model (attempt {i+1})...")

                if model_url.endswith('.zip'):
                    # Handle zip file download and extraction
                    import zipfile
                    zip_path = models_dir / "model.zip"
                    urllib.request.urlretrieve(model_url, zip_path)

                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(models_dir)

                    # Look for ONNX files in extracted content
                    onnx_files = list(models_dir.glob("**/*.onnx"))
                    if onnx_files:
                        # Use the first ONNX file found
                        extracted_model = onnx_files[0]
                        extracted_model.rename(model_path)
                        logger.info(f"✅ Model extracted and renamed: {model_path}")
                        zip_path.unlink()  # Remove zip file
                        return str(model_path)
                else:
                    # Direct ONNX file download
                    urllib.request.urlretrieve(model_url, model_path)
                    logger.info(f"✅ Model downloaded: {model_path}")
                    return str(model_path)

            except Exception as e:
                logger.warning(f"⚠️ Failed to download from {model_url}: {e}")
                continue

        # If all downloads fail, create a placeholder and warn user
        logger.error("❌ Failed to download any model")
        logger.warning("⚠️ Please manually download a MobileFaceNet ONNX model to:")
        logger.warning(f"   {model_path}")
        logger.warning("   Recommended: Use InsightFace buffalo_l model or similar")

        # Return the path anyway - the initialization will fail gracefully
        return str(model_path)
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """
        Preprocess face image for MobileFaceNet inference
        
        Args:
            face_image: Face crop image (BGR format)
            
        Returns:
            Preprocessed image tensor
        """
        if face_image is None or face_image.size == 0:
            raise ValueError("Invalid face image")
        
        # Resize to model input size
        face_resized = cv2.resize(face_image, self.input_size, interpolation=cv2.INTER_LINEAR)
        
        # Convert BGR to RGB
        face_rgb = cv2.cvtColor(face_resized, cv2.COLOR_BGR2RGB)
        
        # Normalize to [0, 1]
        face_normalized = face_rgb.astype(np.float32) / 255.0
        
        # Normalize to [-1, 1] (common for face recognition models)
        face_normalized = (face_normalized - 0.5) / 0.5
        
        # Add batch dimension and transpose to NCHW format
        face_tensor = np.transpose(face_normalized, (2, 0, 1))  # HWC to CHW
        face_tensor = np.expand_dims(face_tensor, axis=0)  # Add batch dimension
        
        return face_tensor
    
    def extract_embedding(self, face_image: np.ndarray) -> Optional[np.ndarray]:
        """
        Extract face embedding from face image
        
        Args:
            face_image: Face crop image (BGR format)
            
        Returns:
            Normalized face embedding vector or None if failed
        """
        if self.compiled_model is None:
            logger.error("❌ Model not initialized")
            return None
        
        try:
            start_time = time.time()
            
            # Preprocess the face image
            input_tensor = self.preprocess_face(face_image)
            
            # Run inference
            result = self.compiled_model([input_tensor])
            embedding = result[self.output_layer]
            
            # Get the embedding vector (remove batch dimension)
            embedding = embedding[0]
            
            # Normalize the embedding
            embedding = embedding / np.linalg.norm(embedding)
            
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            self.total_inferences += 1
            
            logger.debug(f"🔍 MobileFaceNet embedding extracted in {inference_time*1000:.1f}ms")
            
            return embedding
            
        except Exception as e:
            logger.error(f"❌ Error extracting embedding: {e}")
            return None
    
    def extract_embeddings_batch(self, face_images: List[np.ndarray]) -> List[Optional[np.ndarray]]:
        """
        Extract embeddings from multiple face images
        
        Args:
            face_images: List of face crop images
            
        Returns:
            List of embeddings (same order as input)
        """
        embeddings = []
        
        for face_image in face_images:
            embedding = self.extract_embedding(face_image)
            embeddings.append(embedding)
        
        return embeddings
    
    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Compute cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score (0.0 to 1.0)
        """
        try:
            # Ensure embeddings are normalized
            embedding1 = embedding1 / np.linalg.norm(embedding1)
            embedding2 = embedding2 / np.linalg.norm(embedding2)
            
            # Compute cosine similarity
            similarity = np.dot(embedding1, embedding2)
            
            # Clamp to [0, 1] range
            similarity = max(0.0, min(1.0, similarity))
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"❌ Error computing similarity: {e}")
            return 0.0
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        if not self.inference_times:
            return {
                "avg_inference_time_ms": 0,
                "min_inference_time_ms": 0,
                "max_inference_time_ms": 0,
                "total_inferences": 0
            }
        
        inference_times_ms = [t * 1000 for t in self.inference_times]
        
        return {
            "avg_inference_time_ms": np.mean(inference_times_ms),
            "min_inference_time_ms": np.min(inference_times_ms),
            "max_inference_time_ms": np.max(inference_times_ms),
            "total_inferences": self.total_inferences
        }

# Global instance for the application
mobilefacenet_model = None

def get_mobilefacenet_model(model_path: Optional[str] = None) -> MobileFaceNetOpenVINO:
    """Get or create global MobileFaceNet model instance"""
    global mobilefacenet_model
    if mobilefacenet_model is None:
        mobilefacenet_model = MobileFaceNetOpenVINO(model_path)
    return mobilefacenet_model
