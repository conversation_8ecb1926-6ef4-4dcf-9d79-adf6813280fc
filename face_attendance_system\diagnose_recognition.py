#!/usr/bin/env python3
"""
Comprehensive face recognition diagnostic script
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.models import User, Camera
from app.services.camera_manager import camera_manager
from app.services.new_face_recognition import get_new_face_recognition_system
from app.services.face_utils import face_recognition_system
import cv2
import numpy as np
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_complete_pipeline():
    """Test the complete face recognition pipeline"""
    logger.info("🔍 COMPREHENSIVE FACE RECOGNITION DIAGNOSTIC")
    logger.info("=" * 60)
    
    db = SessionLocal()
    try:
        # Step 1: Check database
        logger.info("\n📊 STEP 1: Database Check")
        users = db.query(User).all()
        cameras = db.query(Camera).all()
        
        logger.info(f"Users in database: {len(users)}")
        for user in users:
            logger.info(f"  - {user.name} (ID: {user.id}) - Has encoding: {user.face_encoding is not None}")
            if user.face_encoding:
                logger.info(f"    Encoding length: {len(user.face_encoding)} bytes")
        
        logger.info(f"Cameras in database: {len(cameras)}")
        for camera in cameras:
            logger.info(f"  - {camera.name} (ID: {camera.id}) - URL: {camera.url} - Type: {camera.type}")
        
        if not users or not any(user.face_encoding for user in users):
            logger.error("❌ No users with face encodings found!")
            return
        
        if not cameras:
            logger.error("❌ No cameras found!")
            return
        
        # Step 2: Test camera manager initialization
        logger.info("\n🎥 STEP 2: Camera Manager Test")
        test_camera = cameras[0]  # Use first camera
        
        logger.info(f"Testing camera: {test_camera.name} (ID: {test_camera.id})")
        logger.info(f"Active cameras before start: {list(camera_manager.active_cameras.keys())}")
        
        # Start the camera (this should load faces)
        success = camera_manager.start_camera(
            test_camera.id, test_camera.name, test_camera.url, test_camera.type, db
        )
        
        logger.info(f"Camera start result: {success}")
        logger.info(f"Active cameras after start: {list(camera_manager.active_cameras.keys())}")
        
        if not success:
            logger.error("❌ Failed to start camera!")
            return
        
        # Step 3: Check face recognition system state
        logger.info("\n🧠 STEP 3: Face Recognition System State")
        
        if camera_manager.use_new_recognition and camera_manager.new_face_system:
            new_system = camera_manager.new_face_system
            logger.info(f"NEW system - Known faces: {len(new_system.known_encodings)}")
            logger.info(f"NEW system - Known names: {new_system.known_names}")
            logger.info(f"NEW system - Known user IDs: {new_system.known_user_ids}")
            
            # Check fallback system
            if hasattr(new_system, 'fallback_system') and new_system.fallback_system:
                fallback = new_system.fallback_system
                logger.info(f"Fallback system - Known faces: {len(fallback.known_encodings)}")
                logger.info(f"Fallback system - Known names: {fallback.known_names}")
                logger.info(f"Fallback system - Known user IDs: {fallback.known_user_ids}")
        
        # Step 4: Test with webcam frame
        logger.info("\n📷 STEP 4: Live Webcam Test")
        
        # Try to capture a frame from webcam
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                logger.info(f"✅ Captured frame: {frame.shape}")
                
                # Test face recognition on this frame
                if camera_manager.use_new_recognition and camera_manager.new_face_system:
                    logger.info("Testing NEW face recognition system...")
                    results = camera_manager.new_face_system.recognize_faces_in_frame(frame)
                    logger.info(f"Recognition results: {len(results)} faces")
                    
                    for i, result in enumerate(results):
                        logger.info(f"  Face {i+1}:")
                        logger.info(f"    Name: {result.get('name', 'N/A')}")
                        logger.info(f"    Is known: {result.get('is_known', 'N/A')}")
                        logger.info(f"    Confidence: {result.get('confidence', 'N/A')}")
                        logger.info(f"    User ID: {result.get('user_id', 'N/A')}")
                        logger.info(f"    Location: {result.get('location', 'N/A')}")
                
                # Also test old system for comparison
                logger.info("Testing OLD face recognition system...")
                old_results = face_recognition_system.recognize_faces_in_frame(frame)
                logger.info(f"OLD system results: {len(old_results)} faces")
                
                for i, result in enumerate(old_results):
                    logger.info(f"  Face {i+1}:")
                    logger.info(f"    Name: {result.get('name', 'N/A')}")
                    logger.info(f"    Is known: {result.get('is_known', 'N/A')}")
                    logger.info(f"    Confidence: {result.get('confidence', 'N/A')}")
                    logger.info(f"    User ID: {result.get('user_id', 'N/A')}")
                
            else:
                logger.error("❌ Could not capture frame from webcam")
            cap.release()
        else:
            logger.error("❌ Could not open webcam")
        
        # Step 5: Test with registered user's image
        logger.info("\n🖼️ STEP 5: Registered User Image Test")
        
        user_with_encoding = next((u for u in users if u.face_encoding), None)
        if user_with_encoding and user_with_encoding.image_path:
            image_path = user_with_encoding.image_path
            if image_path.startswith('/static/'):
                image_path = image_path.replace('/static/', 'app/static/')
            
            logger.info(f"Testing with {user_with_encoding.name}'s image: {image_path}")
            
            if os.path.exists(image_path):
                test_image = cv2.imread(image_path)
                if test_image is not None:
                    logger.info(f"✅ Loaded test image: {test_image.shape}")
                    
                    # Test recognition on registered user's image
                    if camera_manager.use_new_recognition and camera_manager.new_face_system:
                        results = camera_manager.new_face_system.recognize_faces_in_frame(test_image)
                        logger.info(f"Recognition on registered image: {len(results)} faces")
                        
                        for i, result in enumerate(results):
                            logger.info(f"  Face {i+1}:")
                            logger.info(f"    Name: {result.get('name', 'N/A')}")
                            logger.info(f"    Is known: {result.get('is_known', 'N/A')}")
                            logger.info(f"    Confidence: {result.get('confidence', 'N/A')}")
                            logger.info(f"    User ID: {result.get('user_id', 'N/A')}")
                            
                            if result.get('is_known') and result.get('name') == user_with_encoding.name:
                                logger.info("✅ SUCCESS: Registered user recognized correctly!")
                            elif result.get('is_known'):
                                logger.warning(f"⚠️ MISMATCH: Expected {user_with_encoding.name}, got {result.get('name')}")
                            else:
                                logger.error(f"❌ FAILURE: Registered user not recognized (confidence: {result.get('confidence')})")
                else:
                    logger.error(f"❌ Could not load image: {image_path}")
            else:
                logger.error(f"❌ Image file not found: {image_path}")
        
        # Cleanup
        camera_manager.stop_camera(test_camera.id)
        
    finally:
        db.close()

if __name__ == "__main__":
    test_complete_pipeline()
