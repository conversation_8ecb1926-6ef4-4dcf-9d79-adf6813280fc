<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Viewer - {{ camera.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            margin: 0;
            padding: 10px;
        }
        .camera-container {
            text-align: center;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .camera-stream {
            max-width: 100%;
            max-height: 70vh;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }
        .camera-info {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-active { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .status-loading { background-color: #ffc107; }
        .error-message {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .loading-message {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="camera-container">
        <div class="camera-info">
            <h4><i class="fas fa-video"></i> {{ camera.name }}</h4>
            <p class="mb-1">
                <span id="status-indicator" class="status-indicator status-loading"></span>
                <span id="status-text">Connecting...</span>
            </p>
            <small class="text-muted">Camera ID: {{ camera.id }} | Type: {{ camera.type }}</small>
        </div>

        <div id="stream-container">
            <div id="loading-message" class="loading-message">
                <i class="fas fa-spinner fa-spin"></i> Starting camera stream...
            </div>
            <img id="camera-stream" class="camera-stream" style="display: none;" alt="Camera Stream">
            <div id="error-message" class="error-message" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="error-text">Camera stream unavailable</span>
            </div>
        </div>

        <div class="mt-3">
            <button id="refresh-btn" class="btn btn-primary" onclick="refreshStream()">
                <i class="fas fa-refresh"></i> Refresh
            </button>
            <button class="btn btn-secondary" onclick="window.close()">
                <i class="fas fa-times"></i> Close
            </button>
        </div>
    </div>

    <script>
        const cameraId = {{ camera.id }};
        const streamImg = document.getElementById('camera-stream');
        const loadingMessage = document.getElementById('loading-message');
        const errorMessage = document.getElementById('error-message');
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const refreshBtn = document.getElementById('refresh-btn');
        
        let streamUrl = `/camera-stream/${cameraId}?t=${Date.now()}`;
        let retryCount = 0;
        const maxRetries = 3;
        
        function updateStatus(status, text) {
            statusIndicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }
        
        function showError(message) {
            loadingMessage.style.display = 'none';
            streamImg.style.display = 'none';
            errorMessage.style.display = 'block';
            document.getElementById('error-text').textContent = message;
            updateStatus('stopped', 'Camera Stopped');
        }
        
        function showLoading() {
            loadingMessage.style.display = 'block';
            streamImg.style.display = 'none';
            errorMessage.style.display = 'none';
            updateStatus('loading', 'Connecting...');
        }
        
        function showStream() {
            loadingMessage.style.display = 'none';
            streamImg.style.display = 'block';
            errorMessage.style.display = 'none';
            updateStatus('active', 'Live Stream');
            retryCount = 0;
        }
        
        function startStream() {
            showLoading();
            
            // Test if stream is available
            fetch(streamUrl, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        streamImg.src = streamUrl;
                        streamImg.onload = showStream;
                        streamImg.onerror = handleStreamError;
                    } else if (response.status === 410) {
                        showError('Camera has been stopped');
                    } else {
                        handleStreamError();
                    }
                })
                .catch(error => {
                    console.error('Stream test failed:', error);
                    handleStreamError();
                });
        }
        
        function handleStreamError() {
            if (retryCount < maxRetries) {
                retryCount++;
                updateStatus('loading', `Retrying... (${retryCount}/${maxRetries})`);
                setTimeout(() => {
                    streamUrl = `/camera-stream/${cameraId}?t=${Date.now()}`;
                    startStream();
                }, 2000);
            } else {
                showError('Camera stream unavailable. Camera may be stopped or disconnected.');
            }
        }
        
        function refreshStream() {
            retryCount = 0;
            streamUrl = `/camera-stream/${cameraId}?t=${Date.now()}`;
            startStream();
        }
        
        // Start the stream when page loads
        window.onload = function() {
            startStream();
            
            // Check stream status periodically
            setInterval(() => {
                if (streamImg.style.display === 'block') {
                    // Ping the stream to check if it's still alive
                    fetch(`/camera-stream/${cameraId}`, { method: 'HEAD' })
                        .then(response => {
                            if (response.status === 410) {
                                showError('Camera has been stopped');
                            }
                        })
                        .catch(() => {
                            // Stream might be down, but don't immediately show error
                            // Let the image onerror handle it
                        });
                }
            }, 5000); // Check every 5 seconds
        };
        
        // Handle window close
        window.onbeforeunload = function() {
            // Clean up if needed
        };
    </script>
</body>
</html>
