#!/usr/bin/env python3
"""
Fix user face encoding to be compatible with InsightFace
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.models import User
from app.services.face_utils import face_recognition_system
import cv2
import numpy as np
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_user_encoding():
    """Fix user face encoding by re-extracting from image"""
    db = SessionLocal()
    try:
        # Get the user
        user = db.query(User).filter(User.name == "Reshma").first()
        if not user:
            logger.error("User 'Reshma' not found")
            return
        
        logger.info(f"Found user: {user.name} (ID: {user.id})")
        logger.info(f"Current image path: {user.image_path}")

        # Fix the image path
        if user.image_path and user.image_path.startswith('/static/'):
            # Convert to relative path
            image_path = user.image_path.replace('/static/', 'app/static/')
        else:
            image_path = user.image_path

        logger.info(f"Checking image path: {image_path}")

        if not image_path or not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            logger.info("Please provide a valid image path for the user")
            return
        
        # Extract new face encoding using InsightFace
        logger.info("🔄 Extracting new face encoding using InsightFace...")
        
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Could not load image: {image_path}")
                return

            # Extract face encoding using InsightFace
            new_encoding_bytes = face_recognition_system.extract_face_encoding(image_path)

            if new_encoding_bytes is not None:
                # Convert bytes back to numpy array to check
                new_encoding = np.frombuffer(new_encoding_bytes, dtype=np.float32)
                logger.info(f"✅ New encoding extracted: shape {new_encoding.shape}, norm {np.linalg.norm(new_encoding):.3f}")

                # Verify the encoding is valid
                if new_encoding.shape[0] == 512 and not np.isnan(new_encoding).any():
                    # Update user in database
                    user.face_encoding = new_encoding_bytes
                    db.commit()

                    logger.info(f"✅ User {user.name} face encoding updated successfully!")
                    logger.info(f"   New encoding shape: {new_encoding.shape}")
                    logger.info(f"   New encoding norm: {np.linalg.norm(new_encoding):.3f}")

                    # Verify the update
                    db.refresh(user)
                    if user.face_encoding == new_encoding_bytes:
                        logger.info("✅ Database update verified!")
                    else:
                        logger.error("❌ Database update failed!")
                else:
                    logger.error(f"❌ Invalid encoding: shape={new_encoding.shape}, has_nan={np.isnan(new_encoding).any()}")
            else:
                logger.error("❌ Failed to extract face encoding from image")
                
        except Exception as e:
            logger.error(f"❌ Error extracting face encoding: {e}")
    
    finally:
        db.close()

def test_webcam_registration():
    """Test registering face from webcam"""
    logger.info("🔄 Testing webcam face registration...")
    
    # Initialize webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        logger.error("❌ Could not open webcam")
        return
    
    logger.info("📷 Webcam opened. Press SPACE to capture face, ESC to exit")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Display frame
        cv2.imshow('Face Registration - Press SPACE to capture', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # ESC
            break
        elif key == 32:  # SPACE
            logger.info("📸 Capturing face...")
            
            # Save temporary image
            temp_path = "temp_face.jpg"
            cv2.imwrite(temp_path, frame)
            
            # Extract face encoding
            try:
                encoding = face_recognition_system.extract_face_encoding(temp_path)
                if encoding is not None:
                    logger.info(f"✅ Face encoding extracted: shape {encoding.shape}")
                    
                    # Update user in database
                    db = SessionLocal()
                    try:
                        user = db.query(User).filter(User.name == "Reshma").first()
                        if user:
                            user.face_encoding = encoding.tobytes()
                            user.image_path = temp_path
                            db.commit()
                            logger.info(f"✅ User {user.name} updated with new face encoding!")
                        else:
                            logger.error("User not found")
                    finally:
                        db.close()
                    
                    break
                else:
                    logger.error("❌ No face detected in image")
                    
            except Exception as e:
                logger.error(f"❌ Error extracting face: {e}")
    
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "webcam":
        test_webcam_registration()
    else:
        fix_user_encoding()
