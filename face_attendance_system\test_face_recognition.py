#!/usr/bin/env python3
"""
Test script to verify face recognition is working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.models import User
from app.services.new_face_recognition import get_new_face_recognition_system
from app.services.face_utils import face_recognition_system
import numpy as np
import pickle
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_face_recognition():
    """Test face recognition system"""
    logger.info("🔄 Testing Face Recognition System...")
    
    # Get database session
    db = SessionLocal()
    try:
        # Load users from database
        users = db.query(User).all()
        users_data = [
            {
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            }
            for user in users if user.face_encoding
        ]
        logger.info(f"📊 Found {len(users_data)} users with face encodings")
        
        # Test NEW face recognition system
        logger.info("\n=== Testing NEW Face Recognition System ===")
        try:
            new_system = get_new_face_recognition_system(
                similarity_threshold=0.4,
                detection_confidence=0.3
            )
            
            # Clear and load faces
            new_system.clear_known_faces()
            
            for user_data in users_data:
                if user_data['face_encoding']:
                    encoding = user_data['face_encoding']
                    if isinstance(encoding, bytes):
                        try:
                            # Try direct numpy frombuffer as float32 first (InsightFace format)
                            encoding = np.frombuffer(encoding, dtype=np.float32)
                            if len(encoding) in [128, 512]:  # Valid InsightFace dimensions
                                logger.info(f"✅ Loaded raw float32 encoding for {user_data['name']}: shape {encoding.shape}")
                            else:
                                # Try pickle format (older format)
                                encoding = pickle.loads(user_data['face_encoding'])
                                logger.info(f"✅ Loaded pickled encoding for {user_data['name']}: shape {encoding.shape}")
                        except Exception as raw_error:
                            try:
                                # Fallback to pickle
                                encoding = pickle.loads(user_data['face_encoding'])
                                logger.info(f"✅ Loaded pickled encoding for {user_data['name']}: shape {encoding.shape}")
                            except Exception as pickle_error:
                                logger.error(f"❌ Failed to load encoding for {user_data['name']}: raw={raw_error}, pickle={pickle_error}")
                                continue
                    
                    # Validate and normalize
                    if encoding is not None and hasattr(encoding, 'shape'):
                        if len(encoding.shape) == 1 and encoding.shape[0] > 0:
                            if np.linalg.norm(encoding) > 0:
                                encoding = encoding / np.linalg.norm(encoding)
                            
                            new_system.add_known_face(
                                encoding, user_data['name'], user_data['id']
                            )
                            logger.info(f"✅ Added face encoding for {user_data['name']} (shape: {encoding.shape})")
                        else:
                            logger.warning(f"⚠️ Invalid encoding shape for {user_data['name']}: {encoding.shape}")
            
            logger.info(f"🔍 NEW system loaded {len(new_system.known_encodings)} faces")
            logger.info(f"🔍 Known names: {new_system.known_names}")
            logger.info(f"🔍 Known user IDs: {new_system.known_user_ids}")
            
            # Test fallback system
            if hasattr(new_system, 'fallback_system') and new_system.fallback_system:
                logger.info("\n=== Testing Fallback ArcFace System ===")
                fallback = new_system.fallback_system
                
                # Load faces into fallback
                fallback.load_known_faces(users_data)
                logger.info(f"🔍 Fallback system loaded {len(fallback.known_encodings)} faces")
                logger.info(f"🔍 Known names: {fallback.known_names}")
                logger.info(f"🔍 Known user IDs: {fallback.known_user_ids}")
                
                # Check encoding details
                for i, (name, encoding) in enumerate(zip(fallback.known_names, fallback.known_encodings)):
                    logger.info(f"   Face {i}: {name} - shape: {encoding.shape}, norm: {np.linalg.norm(encoding):.3f}")
            
        except Exception as e:
            logger.error(f"❌ Error testing NEW system: {e}")
        
        # Test OLD face recognition system
        logger.info("\n=== Testing OLD Face Recognition System ===")
        try:
            face_recognition_system.load_known_faces(users_data)
            logger.info(f"🔍 OLD system loaded {len(face_recognition_system.known_encodings)} faces")
            logger.info(f"🔍 Known names: {face_recognition_system.known_names}")
            logger.info(f"🔍 Known user IDs: {face_recognition_system.known_user_ids}")
            
            # Check encoding details
            for i, (name, encoding) in enumerate(zip(face_recognition_system.known_names, face_recognition_system.known_encodings)):
                logger.info(f"   Face {i}: {name} - shape: {encoding.shape}, norm: {np.linalg.norm(encoding):.3f}")
                
        except Exception as e:
            logger.error(f"❌ Error testing OLD system: {e}")
    
    finally:
        db.close()

if __name__ == "__main__":
    test_face_recognition()
