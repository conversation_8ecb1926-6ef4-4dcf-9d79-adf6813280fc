<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Attendance System - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .card-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .camera-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .camera-active { background-color: #28a745; }
        .camera-inactive { background-color: #dc3545; }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        .camera-feed {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }
        .camera-feed-container {
            position: relative;
            margin-bottom: 10px;
        }
        .camera-label {
            position: absolute;
            top: 5px;
            left: 5px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .no-camera-placeholder {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-camera"></i> Face Attendance System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance Logs
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h1 class="mb-4">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </h1>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Currently Logged In</h5>
                                <h2>{{ current_users|length }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Active Cameras</h5>
                                <h2>{{ active_cameras|length }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-video fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Today's Attendance</h5>
                                <h2>{{ today_logs|length }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">System Status</h5>
                                <h6 class="text-success">
                                    <i class="fas fa-check-circle"></i> Online
                                </h6>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-server fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Camera Feeds -->
        {% if active_cameras %}
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-video"></i> Live Camera Feeds</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for camera in active_cameras %}
                            <div class="col-md-4 col-lg-3 mb-3">
                                <div class="camera-feed-container">
                                    <img src="/camera-stream/{{ camera.camera_id }}"
                                         alt="{{ camera.camera_name }}"
                                         class="camera-feed"
                                         data-camera-id="{{ camera.camera_id }}"
                                         onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                                         onerror="handleCameraError(this);">
                                    <div class="no-camera-placeholder" style="display: none;">
                                        <i class="fas fa-video-slash fa-2x mb-2"></i>
                                        <small>Camera Offline</small>
                                    </div>
                                    <div class="camera-label">
                                        {{ camera.camera_name }} ({{ camera.camera_type }})
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Currently Logged In Users -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-check"></i> Currently Logged In</h5>
                    </div>
                    <div class="card-body" id="current-users-list">
                        {% if current_users %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Login Time</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody id="current-users-tbody">
                                        {% for user in current_users %}
                                        <tr>
                                            <td>
                                                <a href="/user/{{ user.user_id }}" class="text-decoration-none">
                                                    {{ user.name }}
                                                </a>
                                            </td>
                                            <td>{{ user.login_time }}</td>
                                            <td>{{ user.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{% if user.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ user.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">No users currently logged in.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Active Cameras -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-video"></i> Active Cameras</h5>
                    </div>
                    <div class="card-body">
                        {% if active_cameras %}
                            {% for camera in active_cameras %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <span class="camera-status {% if camera.is_active %}camera-active{% else %}camera-inactive{% endif %}"></span>
                                    <strong>{{ camera.camera_name }}</strong>
                                    <small class="text-muted">({{ camera.camera_type }})</small>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewCamera({{ camera.camera_id }})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="stopCamera({{ camera.camera_id }})">
                                        <i class="fas fa-stop"></i> Stop
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No active cameras.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Attendance Logs -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-history"></i> Recent Attendance Logs</h5>
                        <a href="/attendance" class="btn btn-sm btn-primary">View All</a>
                    </div>
                    <div class="card-body">
                        {% if today_logs %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Date</th>
                                            <th>Login</th>
                                            <th>Logout</th>
                                            <th>Duration</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-attendance-tbody">
                                        {% for log in today_logs %}
                                        <tr>
                                            <td>
                                                <a href="/user/{{ log.user_id }}" class="text-decoration-none">
                                                    {{ log.name }}
                                                </a>
                                            </td>
                                            <td>{{ log.date }}</td>
                                            <td>{{ log.login_time or '-' }}</td>
                                            <td>{{ log.logout_time or '-' }}</td>
                                            <td>{{ log.duration or '-' }}</td>
                                            <td>{{ log.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{% if log.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ log.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">No attendance logs for today.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCamera(cameraId) {
            // Store reference to the camera window - open camera viewer page instead of raw stream
            const cameraWindow = window.open(`/camera-viewer/${cameraId}`, `camera_${cameraId}`, 'width=800,height=600');

            // Store window reference for later use
            if (!window.cameraWindows) {
                window.cameraWindows = {};
            }
            window.cameraWindows[cameraId] = cameraWindow;
        }

        function stopCamera(cameraId) {
            if (confirm('Are you sure you want to stop this camera?')) {
                fetch(`/stop-camera/${cameraId}`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.message) {
                            alert(data.message);

                            // Close the camera window if it's open
                            if (window.cameraWindows && window.cameraWindows[cameraId]) {
                                try {
                                    window.cameraWindows[cameraId].close();
                                    delete window.cameraWindows[cameraId];
                                } catch (e) {
                                    console.log('Camera window already closed or inaccessible');
                                }
                            }

                            location.reload();
                        } else {
                            alert(data.error);
                        }
                    })
                    .catch(error => {
                        alert('Error stopping camera');
                        console.error(error);
                    });
            }
        }

        // Handle camera feed errors
        function handleCameraError(img) {
            console.log('Camera feed error for camera:', img.dataset.cameraId);
            img.style.display = 'none';
            img.nextElementSibling.style.display = 'flex';

            // Try to reconnect after 3 seconds
            setTimeout(() => {
                const cameraId = img.dataset.cameraId;
                const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                img.src = newSrc;
            }, 3000);
        }

        // Initialize camera feeds with proper error handling
        function initializeCameraFeeds() {
            const cameraFeeds = document.querySelectorAll('.camera-feed');
            cameraFeeds.forEach(feed => {
                // Add timestamp to prevent caching
                const cameraId = feed.dataset.cameraId;
                feed.src = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;

                // Set up periodic refresh for each feed
                setInterval(() => {
                    if (feed.style.display !== 'none') {
                        const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                        feed.src = newSrc;
                    }
                }, 100); // Refresh every 100ms for smooth video
            });
        }

        // Initialize feeds when page loads
        document.addEventListener('DOMContentLoaded', initializeCameraFeeds);

        // Real-time dashboard updates
        function updateDashboardData() {
            // Add cache-busting parameter to prevent caching issues with RTSP cameras
            const timestamp = new Date().getTime();

            // Update recent attendance logs with cache busting
            fetch(`/api/attendance/recent?_=${timestamp}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateRecentAttendanceLogs(data.logs);
                    }
                })
                .catch(error => console.error('Error fetching recent attendance:', error));

            // Update currently logged in users with cache busting
            fetch(`/api/attendance/current?_=${timestamp}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateCurrentlyLoggedIn(data.users);
                    }
                })
                .catch(error => console.error('Error fetching current users:', error));
        }

        function updateRecentAttendanceLogs(logs) {
            const tbody = document.querySelector('#recent-attendance-tbody');
            if (!tbody) return;

            tbody.innerHTML = '';
            logs.slice(0, 10).forEach(log => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${log.name}</td>
                    <td>${log.date}</td>
                    <td>${log.login_time || '-'}</td>
                    <td>${log.logout_time || '-'}</td>
                    <td>${log.duration || '-'}</td>
                    <td>${log.camera_name}</td>
                    <td>
                        <span class="badge ${log.camera_type === 'IN' ? 'bg-success' : 'bg-warning'}">
                            ${log.camera_type}
                        </span>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update today's attendance count
            const todayCount = logs.filter(log => log.date === new Date().toISOString().split('T')[0]).length;
            const todayCountElement = document.querySelector('.card-stats:nth-child(3) h2');
            if (todayCountElement) {
                todayCountElement.textContent = todayCount;
            }
        }

        function updateCurrentlyLoggedIn(users) {
            const currentUsersContainer = document.querySelector('#current-users-list');
            const currentUsersCount = document.querySelector('.card-stats:nth-child(1) h2');
            const currentUsersTbody = document.querySelector('#current-users-tbody');

            if (currentUsersCount) {
                currentUsersCount.textContent = users.length;
            }

            if (currentUsersContainer) {
                if (users.length === 0) {
                    currentUsersContainer.innerHTML = '<p class="text-muted">No users currently logged in.</p>';
                } else {
                    // Update the table structure
                    currentUsersContainer.innerHTML = `
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Login Time</th>
                                        <th>Camera</th>
                                        <th>Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${users.map(user => `
                                        <tr>
                                            <td>
                                                <a href="/user/${user.user_id}" class="text-decoration-none">
                                                    ${user.name}
                                                </a>
                                            </td>
                                            <td>${user.login_time}</td>
                                            <td>${user.camera_name}</td>
                                            <td>
                                                <span class="badge bg-${user.camera_type === 'IN' ? 'success' : 'warning'}">
                                                    ${user.camera_type}
                                                </span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                }
            }
        }

        // Update dashboard data more frequently for real-time updates
        setInterval(updateDashboardData, 1500); // Reduced to 1.5 seconds for better responsiveness

        // Initial update when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateDashboardData();
        });

        // Full page refresh every 5 minutes as backup
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
