"""
Quick fix script to change camera type from OUT to IN
This will allow proper attendance logging
"""

import sqlite3
import os

def fix_camera_type():
    """Change camera type from OUT to IN for proper attendance logging"""
    
    # Database path - try multiple possible locations
    possible_paths = [
        "app/attendance.db",
        "attendance.db",
        "app/app.db",
        "app.db"
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            print(f"✅ Found database at: {path}")
            break
    
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current cameras
        cursor.execute("SELECT id, name, camera_type FROM cameras")
        cameras = cursor.fetchall()
        
        print("📹 Current cameras:")
        for camera_id, name, camera_type in cameras:
            print(f"   ID: {camera_id}, Name: {name}, Type: {camera_type}")
        
        # Update all cameras to IN type
        cursor.execute("UPDATE cameras SET camera_type = 'IN' WHERE camera_type = 'OUT'")
        updated_count = cursor.rowcount
        
        # Commit changes
        conn.commit()
        
        print(f"\n✅ Updated {updated_count} cameras from OUT to IN type")
        
        # Verify changes
        cursor.execute("SELECT id, name, camera_type FROM cameras")
        cameras = cursor.fetchall()
        
        print("\n📹 Updated cameras:")
        for camera_id, name, camera_type in cameras:
            print(f"   ID: {camera_id}, Name: {name}, Type: {camera_type}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing camera type: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing Camera Type for Proper Attendance Logging")
    print("=" * 60)
    
    success = fix_camera_type()
    
    if success:
        print("\n🎉 Camera type fixed successfully!")
        print("✅ Now cameras will log LOGIN when faces are detected")
        print("🌐 Restart the camera in the web interface to apply changes")
    else:
        print("\n❌ Failed to fix camera type")
        print("💡 You can manually change it in the web interface:")
        print("   1. Go to Manage Camera")
        print("   2. Edit the camera")
        print("   3. Change Type from 'OUT' to 'IN'")
        print("   4. Save changes")
