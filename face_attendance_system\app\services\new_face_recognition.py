"""
New Face Recognition System
MediaPipe Face Detection + MobileFaceNet OpenVINO + Cosine Similarity
Optimized for CPU performance and accuracy
"""

import cv2
import numpy as np
import logging
import time
from typing import List, Dict, Optional, Tuple
from pathlib import Path

from .mediapipe_detector import get_mediapipe_detector
from .mobilefacenet_openvino import get_mobilefacenet_model

logger = logging.getLogger(__name__)

class NewFaceRecognitionSystem:
    """
    Complete face recognition pipeline:
    1. MediaPipe Face Detection (< 5ms)
    2. Face Cropping & Resizing
    3. MobileFaceNet OpenVINO Embedding
    4. Cosine Similarity Matching
    """
    
    def __init__(self, 
                 similarity_threshold: float = 0.6,
                 detection_confidence: float = 0.5,
                 model_path: Optional[str] = None):
        """
        Initialize the new face recognition system
        
        Args:
            similarity_threshold: Cosine similarity threshold for recognition
            detection_confidence: Minimum confidence for face detection
            model_path: Path to MobileFaceNet ONNX model
        """
        self.similarity_threshold = similarity_threshold
        self.detection_confidence = detection_confidence
        
        # Known faces database
        self.known_encodings = []
        self.known_names = []
        self.known_user_ids = []
        
        # Performance metrics
        self.total_detections = 0
        self.successful_recognitions = 0
        self.pipeline_times = []
        
        # Initialize components
        logger.info("🔄 Initializing New Face Recognition System...")
        
        try:
            # Initialize MediaPipe detector
            self.detector = get_mediapipe_detector()
            logger.info("✅ MediaPipe detector initialized")

            # Initialize MobileFaceNet model with fallback
            try:
                self.embedding_model = get_mobilefacenet_model(model_path)
                logger.info("✅ MobileFaceNet model initialized")
                self.use_embedding_model = True
            except Exception as model_error:
                logger.warning(f"⚠️ MobileFaceNet model failed: {model_error}")
                logger.info("🔄 Using MediaPipe-only mode (detection without recognition)")
                self.embedding_model = None
                self.use_embedding_model = False

            logger.info("✅ New Face Recognition System ready!")
            logger.info(f"   Detection confidence: {detection_confidence}")
            logger.info(f"   Similarity threshold: {similarity_threshold}")
            logger.info(f"   Embedding model: {'Available' if self.use_embedding_model else 'MediaPipe-only mode'}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize face recognition system: {e}")
            raise
    
    def add_known_face(self, face_encoding: np.ndarray, name: str, user_id: int):
        """Add a known face to the database"""
        if face_encoding is not None:
            self.known_encodings.append(face_encoding)
            self.known_names.append(name)
            self.known_user_ids.append(user_id)
            logger.info(f"✅ Added known face: {name} (ID: {user_id})")
    
    def clear_known_faces(self):
        """Clear all known faces"""
        self.known_encodings.clear()
        self.known_names.clear()
        self.known_user_ids.clear()
        logger.info("🗑️ Cleared all known faces")
    
    def extract_face_encoding(self, image_path: str) -> Optional[np.ndarray]:
        """
        Extract face encoding from image file for user registration
        
        Args:
            image_path: Path to image file
            
        Returns:
            Face embedding vector or None if no face found
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"❌ Could not load image: {image_path}")
                return None
            
            # Detect faces
            faces = self.detector.detect_faces(image)
            
            if not faces:
                logger.warning(f"⚠️ No face detected in image: {image_path}")
                return None
            
            if len(faces) > 1:
                logger.warning(f"⚠️ Multiple faces detected, using largest one")
                # Sort by area (w * h) and take the largest
                faces = sorted(faces, key=lambda f: f[2] * f[3], reverse=True)
            
            # Get the largest face
            x, y, w, h, confidence = faces[0]
            
            # Extract face crop
            face_crops = self.detector.get_face_crops(image, [faces[0]])
            
            if not face_crops:
                logger.error(f"❌ Failed to crop face from image: {image_path}")
                return None
            
            # Extract embedding
            if self.use_embedding_model and self.embedding_model:
                embedding = self.embedding_model.extract_embedding(face_crops[0])

                if embedding is not None:
                    logger.info(f"✅ Face encoding extracted from {image_path}")
                    return embedding
                else:
                    logger.error(f"❌ Failed to extract embedding from {image_path}")
                    return None
            else:
                logger.warning(f"⚠️ No embedding model available for {image_path}")
                # Return a simple feature vector based on face crop for basic matching
                face_crop = face_crops[0]
                # Create a simple hash-like feature from the face crop
                simple_features = cv2.resize(face_crop, (32, 32)).flatten().astype(np.float32)
                simple_features = simple_features / np.linalg.norm(simple_features)
                logger.info(f"✅ Simple face features extracted from {image_path}")
                return simple_features
                
        except Exception as e:
            logger.error(f"❌ Error extracting face encoding: {e}")
            return None
    
    def recognize_faces_in_frame(self, frame: np.ndarray) -> List[Dict]:
        """
        Recognize faces in a frame using the new pipeline
        
        Args:
            frame: Input BGR image from camera
            
        Returns:
            List of recognition results
        """
        if frame is None or frame.size == 0:
            return []
        
        if len(self.known_encodings) == 0:
            return []
        
        start_time = time.time()
        results = []
        
        try:
            # Step 1: Detect faces with MediaPipe
            faces = self.detector.detect_faces(frame)
            
            if not faces:
                return []
            
            self.total_detections += len(faces)
            
            # Step 2: Extract face crops
            face_crops = self.detector.get_face_crops(frame, faces)
            
            if not face_crops:
                return []
            
            # Step 3: Extract embeddings for each face
            if self.use_embedding_model and self.embedding_model:
                embeddings = self.embedding_model.extract_embeddings_batch(face_crops)
            else:
                # Use simple feature extraction as fallback
                embeddings = []
                for face_crop in face_crops:
                    if face_crop is not None:
                        simple_features = cv2.resize(face_crop, (32, 32)).flatten().astype(np.float32)
                        simple_features = simple_features / np.linalg.norm(simple_features)
                        embeddings.append(simple_features)
                    else:
                        embeddings.append(None)

            # Step 4: Match against known faces
            for i, (face_data, embedding) in enumerate(zip(faces, embeddings)):
                if embedding is None:
                    continue
                
                x, y, w, h, detection_confidence = face_data
                
                # Find best match
                best_match_idx = -1
                best_similarity = 0.0
                
                for j, known_encoding in enumerate(self.known_encodings):
                    if self.use_embedding_model and self.embedding_model:
                        similarity = self.embedding_model.compute_similarity(embedding, known_encoding)
                    else:
                        # Simple cosine similarity for fallback
                        similarity = np.dot(embedding, known_encoding) / (
                            np.linalg.norm(embedding) * np.linalg.norm(known_encoding)
                        )
                        similarity = max(0.0, min(1.0, similarity))

                    if similarity > best_similarity:
                        best_similarity = similarity
                        best_match_idx = j
                
                # Determine if it's a match
                if best_similarity >= self.similarity_threshold:
                    name = self.known_names[best_match_idx]
                    user_id = self.known_user_ids[best_match_idx]
                    is_known = True
                    self.successful_recognitions += 1
                else:
                    name = "Unknown"
                    user_id = None
                    is_known = False
                
                # Convert coordinates to (top, right, bottom, left) format for compatibility
                location = (y, x + w, y + h, x)
                
                results.append({
                    'name': name,
                    'user_id': user_id,
                    'confidence': best_similarity,
                    'location': location,
                    'is_known': is_known,
                    'detection_confidence': detection_confidence
                })
            
            pipeline_time = time.time() - start_time
            self.pipeline_times.append(pipeline_time)
            
            if results:
                logger.debug(f"🔍 Recognized {len(results)} faces in {pipeline_time*1000:.1f}ms")
            
        except Exception as e:
            logger.error(f"❌ Error in face recognition pipeline: {e}")
        
        return results
    
    def draw_face_boxes(self, frame: np.ndarray, recognition_results: List[Dict]) -> np.ndarray:
        """
        Draw face recognition results on frame
        
        Args:
            frame: Input BGR image
            recognition_results: List of recognition results
            
        Returns:
            Frame with drawn boxes and labels
        """
        if frame is None or not recognition_results:
            return frame
        
        result_frame = frame.copy()
        
        for result in recognition_results:
            name = result['name']
            confidence = result['confidence']
            location = result['location']  # (top, right, bottom, left)
            is_known = result['is_known']
            
            top, right, bottom, left = location
            
            # Choose color based on recognition status
            color = (0, 255, 0) if is_known else (0, 0, 255)  # Green for known, Red for unknown
            
            # Draw bounding box
            cv2.rectangle(result_frame, (left, top), (right, bottom), color, 2)
            
            # Prepare label
            if is_known:
                label = f"{name} ({confidence:.2f})"
            else:
                label = "Unknown"
            
            # Draw label background
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 1)[0]
            cv2.rectangle(result_frame, (left, bottom - label_size[1] - 10), 
                         (left + label_size[0], bottom), color, -1)
            
            # Draw label text
            cv2.putText(result_frame, label, (left, bottom - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        return result_frame
    
    def get_performance_stats(self) -> dict:
        """Get comprehensive performance statistics"""
        detector_stats = self.detector.get_performance_stats()
        model_stats = self.embedding_model.get_performance_stats()
        
        pipeline_times_ms = [t * 1000 for t in self.pipeline_times] if self.pipeline_times else [0]
        
        return {
            "pipeline": {
                "avg_total_time_ms": np.mean(pipeline_times_ms),
                "min_total_time_ms": np.min(pipeline_times_ms),
                "max_total_time_ms": np.max(pipeline_times_ms),
                "total_frames_processed": len(self.pipeline_times)
            },
            "detection": detector_stats,
            "embedding": model_stats,
            "recognition": {
                "total_detections": self.total_detections,
                "successful_recognitions": self.successful_recognitions,
                "recognition_rate": (self.successful_recognitions / max(1, self.total_detections)) * 100
            }
        }

# Global instance for the application
new_face_recognition_system = None

def get_new_face_recognition_system(similarity_threshold: float = 0.6,
                                   detection_confidence: float = 0.5,
                                   model_path: Optional[str] = None) -> NewFaceRecognitionSystem:
    """Get or create global face recognition system instance"""
    global new_face_recognition_system
    if new_face_recognition_system is None:
        new_face_recognition_system = NewFaceRecognitionSystem(
            similarity_threshold=similarity_threshold,
            detection_confidence=detection_confidence,
            model_path=model_path
        )
    return new_face_recognition_system
