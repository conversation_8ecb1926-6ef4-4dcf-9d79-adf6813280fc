import cv2
import threading
import time
import logging
import numpy as np
from typing import Dict, Optional, List, Callable
from sqlalchemy.orm import Session
from ..models.models import Camera, User
from ..core.database import SessionLocal
from .face_utils import face_recognition_system
from .new_face_recognition import get_new_face_recognition_system
from .attendance import attendance_service

logger = logging.getLogger(__name__)

class CameraManager:
    def __init__(self, use_new_recognition: bool = True):
        self.active_cameras = {}  # {camera_id: CameraStream}
        self.recognition_callbacks = []
        self.use_new_recognition = use_new_recognition

        # Initialize the appropriate face recognition system
        if self.use_new_recognition:
            try:
                self.new_face_system = get_new_face_recognition_system()
                logger.info("✅ Using NEW MediaPipe + MobileFaceNet recognition system")
            except Exception as e:
                logger.error(f"❌ Failed to initialize new recognition system: {e}")
                logger.info("🔄 Falling back to old ArcFace system")
                self.use_new_recognition = False
                self.new_face_system = None
        else:
            logger.info("✅ Using OLD ArcFace recognition system")
            self.new_face_system = None

    def add_recognition_callback(self, callback: Callable):
        """Add callback function to be called when face is recognized"""
        self.recognition_callbacks.append(callback)

    def start_camera(self, camera_id: int, camera_name: str, rtsp_url: str,
                    camera_type: str, db: Session) -> bool:
        """Start camera stream and face recognition"""
        logger.info(f"🚀 STARTING CAMERA: ID={camera_id}, Name={camera_name}, URL={rtsp_url}, Type={camera_type}")

        if camera_id in self.active_cameras:
            logger.warning(f"Camera {camera_id} is already active")
            return False

        try:
            logger.info(f"📊 Loading known faces from database...")
            # Load known faces from database
            users = db.query(User).all()
            users_data = [
                {
                    'id': user.id,
                    'name': user.name,
                    'face_encoding': user.face_encoding
                }
                for user in users if user.face_encoding
            ]
            logger.info(f"📊 Loaded {len(users_data)} known faces")

            # Load faces into recognition system
            try:
                if self.use_new_recognition and self.new_face_system:
                    # Load faces into new system
                    self.new_face_system.clear_known_faces()
                    for user_data in users_data:
                        if user_data['face_encoding']:
                            # Convert face encoding from bytes to numpy array if needed
                            encoding = user_data['face_encoding']
                            if isinstance(encoding, (bytes, str)):
                                import pickle
                                encoding = pickle.loads(encoding) if isinstance(encoding, bytes) else eval(encoding)
                            self.new_face_system.add_known_face(
                                encoding, user_data['name'], user_data['id']
                            )
                    logger.info(f"✅ NEW face recognition system updated with {len(users_data)} known faces")
                else:
                    # Load faces into old system
                    face_recognition_system.load_known_faces(users_data)
                    logger.info(f"✅ OLD face recognition system updated with {len(users_data)} known faces")
            except Exception as face_load_error:
                logger.error(f"❌ Error loading faces into recognition system: {face_load_error}")
                # Continue without face recognition if loading fails

            # Test camera source before creating stream
            logger.info(f"🔍 Testing camera source: {rtsp_url}")
            try:
                # Parse camera source
                if rtsp_url.isdigit():
                    camera_source = int(rtsp_url)
                else:
                    camera_source = rtsp_url

                logger.info(f"🔍 Parsed camera source: {camera_source}")

                # Quick test
                test_cap = cv2.VideoCapture(camera_source)
                if test_cap.isOpened():
                    ret, frame = test_cap.read()
                    if ret and frame is not None:
                        logger.info(f"✅ Camera source test PASSED - Resolution: {frame.shape[1]}x{frame.shape[0]}")
                        test_cap.release()
                    else:
                        logger.error(f"❌ Camera source test FAILED - Cannot read frames")
                        test_cap.release()
                        return False
                else:
                    logger.error(f"❌ Camera source test FAILED - Cannot open camera")
                    test_cap.release()
                    return False
            except Exception as test_error:
                logger.error(f"❌ Camera source test EXCEPTION: {test_error}")
                return False

            logger.info(f"🎥 Creating camera stream instance...")
            # Create and start camera stream
            camera_stream = CameraStream(
                camera_id, camera_name, rtsp_url, camera_type, db,
                use_new_recognition=self.use_new_recognition,
                new_face_system=self.new_face_system
            )

            logger.info(f"🔄 Starting camera stream...")
            if camera_stream.start():
                self.active_cameras[camera_id] = camera_stream
                logger.info(f"✅ Camera {camera_name} started successfully and added to active cameras")
                logger.info(f"📊 Active cameras count: {len(self.active_cameras)}")
                return True
            else:
                logger.error(f"❌ Failed to start camera {camera_name} - camera_stream.start() returned False")
                return False

        except Exception as e:
            logger.error(f"❌ Exception starting camera {camera_name}: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False

    def stop_camera(self, camera_id: int) -> bool:
        """Stop camera stream"""
        if camera_id in self.active_cameras:
            self.active_cameras[camera_id].stop()
            del self.active_cameras[camera_id]
            logger.info(f"✅ Camera {camera_id} stopped and removed from active list")
            return True
        else:
            logger.warning(f"⚠️ Camera {camera_id} not found in active cameras")
            return False

    def stop_all_cameras(self):
        """Stop all active cameras"""
        for camera_id in list(self.active_cameras.keys()):
            self.stop_camera(camera_id)

    def cleanup_zombie_cameras(self):
        """Remove cameras that are in active list but not actually running"""
        zombie_cameras = []
        for camera_id, camera_stream in self.active_cameras.items():
            if not camera_stream.is_active():
                zombie_cameras.append(camera_id)

        for camera_id in zombie_cameras:
            logger.warning(f"🧟 Removing zombie camera {camera_id}")
            del self.active_cameras[camera_id]

        return len(zombie_cameras)

    def get_camera_frame(self, camera_id: int) -> Optional[bytes]:
        """Get latest frame from camera as JPEG bytes"""
        if camera_id in self.active_cameras:
            camera_stream = self.active_cameras[camera_id]
            if camera_stream.is_active():
                frame = camera_stream.get_frame()
                if frame is None:
                    # Only log occasionally to avoid spam
                    if not hasattr(self, '_last_frame_warning') or time.time() - self._last_frame_warning.get(camera_id, 0) > 3:
                        logger.warning(f"⚠️ Camera {camera_id} is active but returning no frames")
                        if not hasattr(self, '_last_frame_warning'):
                            self._last_frame_warning = {}
                        self._last_frame_warning[camera_id] = time.time()
                return frame
            else:
                logger.warning(f"⚠️ Camera {camera_id} is in active_cameras but not running")
                return None
        else:
            logger.warning(f"⚠️ Camera {camera_id} not found in active cameras. Active: {list(self.active_cameras.keys())}")
            return None

    def get_active_cameras(self) -> List[Dict]:
        """Get list of active cameras"""
        return [
            {
                'camera_id': camera_id,
                'camera_name': stream.camera_name,
                'camera_type': stream.camera_type,
                'is_active': stream.is_active(),
                'last_detection': stream.last_detection_time
            }
            for camera_id, stream in self.active_cameras.items()
        ]

class CameraStream:
    def __init__(self, camera_id: int, camera_name: str, rtsp_url: str,
                 camera_type: str, db: Session,
                 use_new_recognition: bool = True, new_face_system=None):
        self.camera_id = camera_id
        self.camera_name = camera_name
        self.rtsp_url = rtsp_url
        self.camera_type = camera_type
        self.db = db
        self.use_new_recognition = use_new_recognition
        self.new_face_system = new_face_system

        self.cap = None
        self.thread = None
        self.running = False
        self.latest_frame = None
        self.frame_lock = threading.Lock()
        self.last_detection_time = None

        # Recognition settings
        self.detection_interval = 0.5  # Process every 0.5 seconds for more responsive detection
        self.last_process_time = 0
        self.latest_recognition_results = []  # Store latest face recognition results
        self.recognition_lock = threading.Lock()  # Lock for recognition results
        self.last_face_detection_time = 0  # Track when faces were last detected

        # Re-enable face recognition for full functionality
        self.enable_face_recognition = True  # ENABLED - Face recognition with InsightFace ArcFace

        # Simplified mode - no complex processing
        self.simple_mode = True

    def start(self) -> bool:
        """Start camera stream"""
        try:
            # Handle different camera types
            camera_source = self._parse_camera_source(self.rtsp_url)
            logger.info(f"Attempting to open camera: {self.rtsp_url} -> {camera_source}")

            self.cap = cv2.VideoCapture(camera_source)

            # Set camera properties for better image quality
            if self.cap.isOpened():
                # Set resolution for better quality
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

                # Set brightness and contrast for better visibility
                self.cap.set(cv2.CAP_PROP_BRIGHTNESS, 0.5)  # Brightness (0.0 to 1.0)
                self.cap.set(cv2.CAP_PROP_CONTRAST, 0.5)    # Contrast (0.0 to 1.0)
                self.cap.set(cv2.CAP_PROP_SATURATION, 0.5)  # Saturation (0.0 to 1.0)

                # Auto exposure and white balance for better lighting
                self.cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.75)  # Auto exposure
                self.cap.set(cv2.CAP_PROP_AUTO_WB, 1)           # Auto white balance

                # Set FPS for smooth video
                self.cap.set(cv2.CAP_PROP_FPS, 30)

                logger.info(f"📷 Camera properties set for {self.camera_name}")

            if not self.cap.isOpened():
                logger.error(f"Failed to open camera: {self.rtsp_url} (parsed as: {camera_source})")
                logger.error("Possible issues:")
                logger.error("- Webcam: Check if camera index is correct (try 0, 1, 2...)")
                logger.error("- RTSP: Check URL format and network connectivity")
                logger.error("- USB Camera: Check if camera is connected and not in use")
                return False

            # Test if we can read a frame
            logger.info(f"🔍 Testing frame capture from camera {self.camera_name}...")
            ret, test_frame = self.cap.read()
            if not ret or test_frame is None:
                logger.error(f"❌ Camera opened but cannot read frames: {self.rtsp_url}")
                logger.error(f"   ret={ret}, frame={'None' if test_frame is None else 'exists'}")
                self.cap.release()
                return False

            logger.info(f"✅ Camera {self.camera_name} opened successfully!")
            logger.info(f"   Resolution: {test_frame.shape[1]}x{test_frame.shape[0]}")
            logger.info(f"   Frame shape: {test_frame.shape}")
            logger.info(f"   Frame dtype: {test_frame.dtype}")

            # Store the first frame immediately
            with self.frame_lock:
                self.latest_frame = test_frame

            self.running = True
            self.thread = threading.Thread(target=self._process_stream)
            self.thread.daemon = True
            self.thread.start()

            logger.info(f"🚀 Camera {self.camera_name} processing thread started")

            return True

        except Exception as e:
            logger.error(f"Error starting camera stream: {e}")
            return False

    def stop(self):
        """Stop camera stream"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        if self.cap:
            self.cap.release()

    def is_active(self) -> bool:
        """Check if camera is active"""
        return self.running and self.thread and self.thread.is_alive()

    def get_frame(self) -> Optional[bytes]:
        """Get latest frame as JPEG bytes"""
        with self.frame_lock:
            if self.latest_frame is not None:
                try:
                    # Use higher quality JPEG encoding for better video quality
                    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 85]
                    success, buffer = cv2.imencode('.jpg', self.latest_frame, encode_params)
                    if success:
                        return buffer.tobytes()
                    else:
                        logger.error(f"❌ Failed to encode frame for camera {self.camera_name}")
                        return None
                except Exception as e:
                    logger.error(f"❌ Error encoding frame for camera {self.camera_name}: {e}")
                    return None
            else:
                # Only log this occasionally to avoid spam
                if hasattr(self, '_last_no_frame_log'):
                    if time.time() - self._last_no_frame_log > 5:  # Log every 5 seconds
                        logger.warning(f"⚠️ No frame available for camera {self.camera_name}")
                        self._last_no_frame_log = time.time()
                else:
                    logger.warning(f"⚠️ No frame available for camera {self.camera_name}")
                    self._last_no_frame_log = time.time()
        return None

    def _parse_camera_source(self, url: str):
        """Parse camera source URL and return appropriate format for cv2.VideoCapture"""
        try:
            # Check if it's a numeric string (webcam index)
            if url.isdigit():
                return int(url)

            # Check if it's a negative number (sometimes used for webcams)
            if url.startswith('-') and url[1:].isdigit():
                return int(url)

            # For RTSP, HTTP, or other URL formats, return as string
            return url

        except Exception as e:
            logger.error(f"Error parsing camera source '{url}': {e}")
            return url  # Return original if parsing fails

    def _process_stream(self):
        """Main processing loop for camera stream"""
        logger.info(f"🎬 Starting processing for camera {self.camera_name}")
        logger.info(f"🔧 Face recognition enabled: {self.enable_face_recognition}")

        consecutive_failures = 0
        max_failures = 10
        frame_count = 0

        while self.running:
            try:
                ret = False
                frame = None

                # SIMPLIFIED: Just read one frame like the test page
                ret, frame = self.cap.read()

                # Enhance frame brightness and contrast if needed
                if ret and frame is not None:
                    frame = self._enhance_frame_brightness(frame)

                if not ret or frame is None:
                    consecutive_failures += 1
                    if consecutive_failures >= max_failures:
                        logger.error(f"Too many consecutive failures for camera {self.camera_name}")
                        break
                    logger.warning(f"Failed to read frame from camera {self.camera_name} (attempt {consecutive_failures})")
                    time.sleep(0.1)
                    continue

                consecutive_failures = 0  # Reset on successful read
                frame_count += 1

                # Log frame success every 60 frames (about every 2 seconds at 30fps)
                if frame_count % 60 == 0:
                    logger.info(f"✅ Camera {self.camera_name} - Frame {frame_count} captured successfully")

                # Process face recognition at intervals (if enabled)
                current_time = time.time()
                if self.enable_face_recognition and current_time - self.last_process_time >= self.detection_interval:
                    try:
                        self._process_face_recognition(frame)
                        self.last_process_time = current_time
                    except Exception as face_error:
                        logger.error(f"❌ Face recognition error: {face_error}")
                        # Continue camera operation even if face recognition fails

                # Draw detection boxes on the frame using latest recognition results
                frame_with_boxes = frame.copy()
                if self.enable_face_recognition:
                    with self.recognition_lock:
                        if self.latest_recognition_results:
                            try:
                                if self.use_new_recognition and self.new_face_system:
                                    # Use NEW system drawing
                                    frame_with_boxes = self.new_face_system.draw_face_boxes(
                                        frame_with_boxes, self.latest_recognition_results
                                    )
                                else:
                                    # Use OLD system drawing
                                    frame_with_boxes = face_recognition_system.draw_face_boxes(
                                        frame_with_boxes, self.latest_recognition_results
                                    )
                            except Exception as draw_error:
                                logger.error(f"❌ Error drawing face boxes: {draw_error}")
                                frame_with_boxes = frame  # Use original frame if drawing fails

                # Store latest frame
                with self.frame_lock:
                    self.latest_frame = frame_with_boxes

                # Minimal sleep for continuous processing
                time.sleep(0.01)  # Very small delay for CPU efficiency

            except Exception as e:
                consecutive_failures += 1
                logger.error(f"❌ Error in camera processing: {e}")
                if consecutive_failures >= max_failures:
                    logger.error(f"❌ Too many exceptions for camera {self.camera_name}")
                    break
                time.sleep(0.1)

        logger.info(f"🛑 Camera {self.camera_name} processing stopped")
        self.running = False

    def _process_face_recognition(self, frame):
        """Process face recognition on frame with NEW MediaPipe + MobileFaceNet or OLD ArcFace"""
        try:
            # Skip face recognition if frame is invalid
            if frame is None or frame.size == 0:
                logger.warning(f"⚠️ Invalid frame for face recognition in camera {self.camera_name}")
                return

            # Choose recognition system
            try:
                if self.use_new_recognition and self.new_face_system:
                    # Use NEW MediaPipe + MobileFaceNet system
                    recognition_results = self.new_face_system.recognize_faces_in_frame(frame)
                    system_name = "NEW MediaPipe+MobileFaceNet"
                else:
                    # Use OLD ArcFace system
                    recognition_results = face_recognition_system.recognize_faces_in_frame(frame)
                    system_name = "OLD ArcFace"

                # Log detection results occasionally
                if recognition_results and hasattr(self, '_last_detection_log'):
                    if time.time() - self._last_detection_log > 5:  # Log every 5 seconds
                        logger.info(f"🔍 {system_name} detected {len(recognition_results)} faces in camera {self.camera_name}")
                        self._last_detection_log = time.time()
                elif recognition_results:
                    logger.info(f"🔍 {system_name} detected {len(recognition_results)} faces in camera {self.camera_name}")
                    self._last_detection_log = time.time()

            except Exception as face_error:
                logger.error(f"❌ Face recognition error in camera {self.camera_name}: {face_error}")
                # Continue camera operation even if face recognition fails
                recognition_results = []

            # Store recognition results for continuous display
            with self.recognition_lock:
                self.latest_recognition_results = recognition_results
                if recognition_results:
                    self.last_face_detection_time = time.time()
                else:
                    # Clear old results if no faces detected for 3 seconds
                    if time.time() - self.last_face_detection_time > 3.0:
                        self.latest_recognition_results = []

            # Process recognized faces
            for result in recognition_results:
                if result['is_known']:
                    if result['confidence'] > 0.5:
                        logger.info(f"✅ Confidence threshold met: {result['confidence']:.2f} > 0.5")

                        # Create fresh database session for attendance marking
                        db_session = SessionLocal()
                        try:
                            # Mark attendance for known faces
                            attendance_result = attendance_service.mark_attendance(
                                result['user_id'],
                                self.camera_name,
                                self.camera_type,
                                db_session
                            )

                            if attendance_result['status'] in ['login', 'logout']:
                                self.last_detection_time = time.time()
                                logger.info(f"🎯 ATTENDANCE LOGGED: {attendance_result['message']}")
                                logger.info(f"   User: {attendance_result.get('user_name', 'Unknown')}")
                                logger.info(f"   Time: {attendance_result.get('time', 'Unknown')}")
                                logger.info(f"   Type: {attendance_result.get('type', 'Unknown')}")
                                logger.info(f"   Camera: {self.camera_name} ({self.camera_type})")

                                # For RTSP cameras, ensure database sync
                                if self.rtsp_url and not str(self.rtsp_url).isdigit():
                                    logger.info(f"🔄 RTSP camera detected - ensuring database sync")
                                    # Force an explicit commit to ensure changes are visible
                                    db_session.commit()
                                    # Small delay to ensure database sync
                                    time.sleep(0.1)

                            elif attendance_result['status'] == 'ignored':
                                logger.debug(f"Attendance ignored (cooldown): {result.get('name', 'Unknown')}")
                            else:
                                logger.warning(f"Attendance marking failed: {attendance_result.get('message', 'Unknown error')}")

                        except Exception as e:
                            logger.error(f"Error marking attendance for user {result['user_id']}: {e}")
                        finally:
                            # Ensure session is properly closed
                            db_session.close()
                    else:
                        logger.warning(f"❌ Confidence too low: {result['confidence']:.2f} <= 0.5 for {result.get('name', 'Unknown')}")
                else:
                    logger.debug(f"Unknown face detected (confidence: {result['confidence']:.2f})")

        except Exception as e:
            logger.error(f"Error in face recognition processing: {e}")

    def _enhance_frame_brightness(self, frame):
        """Enhance frame brightness and contrast for better visibility"""
        try:
            # Convert to float for processing
            frame_float = frame.astype(np.float32)

            # Apply brightness and contrast enhancement
            # Formula: new_pixel = alpha * pixel + beta
            alpha = 1.2  # Contrast control (1.0-3.0)
            beta = 20    # Brightness control (0-100)

            enhanced = cv2.convertScaleAbs(frame_float, alpha=alpha, beta=beta)

            # Optional: Apply gamma correction for better visibility
            gamma = 1.1
            inv_gamma = 1.0 / gamma
            table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
            enhanced = cv2.LUT(enhanced, table)

            return enhanced

        except Exception as e:
            logger.warning(f"⚠️ Frame enhancement failed: {e}")
            return frame  # Return original frame if enhancement fails

# Utility function to test available cameras
def test_available_cameras(max_cameras=5):
    """Test which camera indices are available"""
    available_cameras = []

    print(f"🔍 Testing camera indices 0-{max_cameras-1}...")

    for i in range(max_cameras):
        print(f"🔍 Testing camera index {i}...")
        cap = cv2.VideoCapture(i)

        if cap.isOpened():
            print(f"✅ Camera {i} opened successfully")
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"✅ Camera {i} can read frames - Resolution: {frame.shape[1]}x{frame.shape[0]}")
                available_cameras.append({
                    'index': i,
                    'status': 'available',
                    'resolution': f"{frame.shape[1]}x{frame.shape[0]}"
                })
            else:
                print(f"❌ Camera {i} opened but cannot read frames")
                available_cameras.append({
                    'index': i,
                    'status': 'opened_but_no_frame'
                })
        else:
            print(f"❌ Camera {i} not available")
            available_cameras.append({
                'index': i,
                'status': 'not_available'
            })
        cap.release()

    working_cameras = [cam for cam in available_cameras if cam.get('status') == 'available']
    print(f"📊 Camera test complete. Working cameras: {working_cameras}")
    return available_cameras

# Global camera manager instance with NEW recognition system
camera_manager = CameraManager(use_new_recognition=True)