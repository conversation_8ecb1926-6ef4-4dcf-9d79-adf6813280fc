import os
from typing import Optional

class Settings:
    # Application settings
    APP_NAME: str = "Face Attendance System"
    VERSION: str = "1.0.0"
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"

    # Database settings
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "mysql+mysqlconnector://root:1234@localhost:3307/attendance_db"
    )

    # Face recognition settings
    FACE_RECOGNITION_TOLERANCE: float = float(os.getenv("FACE_RECOGNITION_TOLERANCE", "0.6"))
    FACE_RECOGNITION_MODEL: str = os.getenv("FACE_RECOGNITION_MODEL", "hog")  # hog for CPU, cnn for GPU

    # Camera settings
    CAMERA_FPS: int = int(os.getenv("CAMERA_FPS", "15"))
    DETECTION_INTERVAL: float = float(os.getenv("DETECTION_INTERVAL", "1.0"))  # seconds
    ATTENDANCE_COOLDOWN: int = int(os.getenv("ATTENDANCE_COOLDOWN", "300"))  # seconds

    # File upload settings
    UPLOAD_FOLDER: str = os.getenv("UPLOAD_FOLDER", "app/static/uploads")
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "5242880"))  # 5MB
    ALLOWED_EXTENSIONS: set = {"png", "jpg", "jpeg", "gif"}

    # Security settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: Optional[str] = os.getenv("LOG_FILE")

    def __init__(self):
        # Create upload folder if it doesn't exist
        os.makedirs(self.UPLOAD_FOLDER, exist_ok=True)

# Global settings instance
settings = Settings()