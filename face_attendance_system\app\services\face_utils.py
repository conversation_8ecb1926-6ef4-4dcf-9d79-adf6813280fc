"""
Enhanced Face Recognition System
CPU-Optimized with fallback support for NumPy 2.x compatibility
"""

import numpy as np
import cv2
from typing import List, Optional, Dict
import logging
import time
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FaceRecognitionSystem:
    def __init__(self, similarity_threshold: float = 0.5, model_name: str = "buffalo_l"):
        """
        Initialize Enhanced Face Recognition System with fallback support

        Args:
            similarity_threshold: Cosine similarity threshold (higher = more strict)
            model_name: Model name (buffalo_l for InsightFace, opencv for OpenCV)
        """
        self.similarity_threshold = similarity_threshold
        self.model_name = model_name
        self.known_encodings = []
        self.known_names = []
        self.known_user_ids = []

        # Performance metrics
        self.detection_times = []
        self.recognition_times = []
        self.total_detections = 0
        self.successful_recognitions = 0

        # Recognition system
        self.app = None
        self.face_cascade = None
        self.recognition_model = None
        self.use_insightface = False

        # Initialize recognition system
        self._initialize_recognition_system()

    def _initialize_recognition_system(self):
        """Initialize recognition system with fallback support"""
        # Try InsightFace first
        if self._try_initialize_insightface():
            self.use_insightface = True
            logger.info("✅ Using InsightFace ArcFace-R100 for face recognition")
        else:
            # Fallback to OpenCV + face_recognition
            if self._try_initialize_opencv():
                self.use_insightface = False
                logger.info("✅ Using OpenCV + face_recognition fallback")
            else:
                logger.error("❌ Failed to initialize any face recognition system")
                raise Exception("No face recognition system available")

    def _try_initialize_insightface(self):
        """Try to initialize InsightFace"""
        try:
            logger.info("🔄 Attempting to initialize InsightFace ArcFace-R100...")

            from insightface.app import FaceAnalysis

            # Initialize FaceAnalysis with CPU providers
            self.app = FaceAnalysis(
                name=self.model_name,
                providers=['CPUExecutionProvider'],
                allowed_modules=['detection', 'recognition']
            )

            # Prepare the model
            self.app.prepare(ctx_id=0, det_size=(640, 640))

            logger.info("✅ InsightFace initialized successfully")
            return True

        except Exception as e:
            logger.warning(f"⚠️ InsightFace initialization failed: {e}")
            logger.info("🔄 Falling back to OpenCV + face_recognition...")
            return False

    def _try_initialize_opencv(self):
        """Try to initialize OpenCV face recognition fallback"""
        try:
            logger.info("🔄 Initializing OpenCV face recognition fallback...")

            # Try to import face_recognition
            import face_recognition

            # Initialize OpenCV face cascade
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)

            if self.face_cascade.empty():
                logger.error("Failed to load face cascade")
                return False

            self.recognition_model = face_recognition
            logger.info("✅ OpenCV + face_recognition fallback initialized")
            return True

        except Exception as e:
            logger.error(f"❌ OpenCV fallback initialization failed: {e}")
            return False

    def extract_face_encoding(self, image_path: str) -> Optional[bytes]:
        """Extract face embedding using available recognition system"""
        if self.use_insightface:
            return self._extract_insightface_embedding(image_path)
        else:
            return self._extract_opencv_embedding(image_path)

    def _extract_insightface_embedding(self, image_path: str) -> Optional[bytes]:
        """Extract face embedding using InsightFace"""
        try:
            if not self.app:
                logger.error("InsightFace not initialized")
                return None

            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Could not load image: {image_path}")
                return None

            start_time = time.time()

            # Detect and analyze faces using InsightFace
            faces = self.app.get(image)

            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)

            if len(faces) == 0:
                logger.warning(f"No face found in image: {image_path}")
                return None
            elif len(faces) > 1:
                logger.warning(f"Multiple faces found in image: {image_path}, using the largest one")
                faces = sorted(faces, key=lambda x: (x.bbox[2] - x.bbox[0]) * (x.bbox[3] - x.bbox[1]), reverse=True)

            # Get the face embedding (512-dimensional vector for ArcFace-R100)
            face = faces[0]
            embedding = face.embedding

            # Normalize the embedding
            embedding = embedding / np.linalg.norm(embedding)

            logger.info(f"✅ InsightFace embedding extracted from {image_path} in {detection_time:.3f}s")

            # Convert to bytes for database storage (using float32 for efficiency)
            return embedding.astype(np.float32).tobytes()

        except Exception as e:
            logger.error(f"❌ Error extracting InsightFace embedding: {e}")
            return None

    def _extract_opencv_embedding(self, image_path: str) -> Optional[bytes]:
        """Extract face embedding using OpenCV + face_recognition fallback"""
        try:
            if not self.recognition_model:
                logger.error("OpenCV recognition model not initialized")
                return None

            start_time = time.time()

            # Load image using face_recognition
            image = self.recognition_model.load_image_file(image_path)
            encodings = self.recognition_model.face_encodings(image, model="large")

            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)

            if encodings:
                logger.info(f"✅ OpenCV embedding extracted from {image_path} in {detection_time:.3f}s")
                return encodings[0].tobytes()
            else:
                logger.warning(f"No face found in image: {image_path}")
                return None

        except Exception as e:
            logger.error(f"❌ Error extracting OpenCV embedding: {e}")
            return None

    def load_known_faces(self, users_data: List[Dict]):
        """Load known faces from database with hybrid embedding support"""
        self.known_encodings = []
        self.known_names = []
        self.known_user_ids = []

        for user in users_data:
            if user['face_encoding']:
                try:
                    encoding = None

                    if self.use_insightface:
                        # Try to load as float32 (InsightFace format)
                        try:
                            encoding = np.frombuffer(user['face_encoding'], dtype=np.float32)
                            if len(encoding) == 512:
                                # Normalize the embedding for InsightFace
                                encoding = encoding / np.linalg.norm(encoding)
                            else:
                                # Try as face_recognition format
                                encoding = np.frombuffer(user['face_encoding'], dtype=np.float64)
                                logger.info(f"Using face_recognition encoding for {user['name']} with InsightFace")
                        except:
                            # Fallback to face_recognition format
                            encoding = np.frombuffer(user['face_encoding'], dtype=np.float64)
                    else:
                        # OpenCV + face_recognition mode
                        try:
                            # Try face_recognition format first
                            encoding = np.frombuffer(user['face_encoding'], dtype=np.float64)
                        except:
                            # Try InsightFace format as fallback
                            encoding = np.frombuffer(user['face_encoding'], dtype=np.float32)
                            logger.info(f"Using InsightFace encoding for {user['name']} with OpenCV")

                    if encoding is not None:
                        self.known_encodings.append(encoding)
                        self.known_names.append(user['name'])
                        self.known_user_ids.append(user['id'])
                    else:
                        logger.warning(f"Could not load encoding for {user['name']}")

                except Exception as e:
                    logger.error(f"Error loading encoding for user {user['name']}: {e}")

        recognition_type = "InsightFace" if self.use_insightface else "OpenCV"
        logger.info(f"✅ Loaded {len(self.known_encodings)} known face embeddings for {recognition_type} recognition")
        if len(self.known_encodings) > 0:
            logger.info(f"   Registered users: {', '.join(self.known_names)}")

            # Check for dimension mismatches
            if self.use_insightface:
                mismatched_users = []
                for i, encoding in enumerate(self.known_encodings):
                    if len(encoding) != 512:  # InsightFace should be 512D
                        mismatched_users.append(self.known_names[i])

                if mismatched_users:
                    logger.warning(f"⚠️ Found {len(mismatched_users)} users with incompatible face encodings: {', '.join(mismatched_users)}")
                    logger.warning(f"⚠️ These users need to re-register their faces for InsightFace compatibility")
                    logger.warning(f"⚠️ Face recognition will not work for these users until they re-register")

    def recognize_faces_in_frame(self, frame) -> List[Dict]:
        """
        Recognize faces in a frame using available recognition system

        Returns:
            List of dictionaries with face information
        """
        if len(self.known_encodings) == 0:
            return []

        if self.use_insightface:
            return self._recognize_insightface(frame)
        else:
            return self._recognize_opencv(frame)

    def _recognize_insightface(self, frame) -> List[Dict]:
        """Recognize faces using InsightFace"""
        if not self.app:
            return []

        results = []

        try:
            start_time = time.time()

            # Detect and analyze faces using InsightFace
            faces = self.app.get(frame)

            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)
            self.total_detections += len(faces)

            if len(faces) > 0:
                logger.debug(f"🔍 InsightFace detected {len(faces)} face(s) in frame")

            for face in faces:
                recognition_start = time.time()

                # Get face embedding
                embedding = face.embedding

                # Normalize the embedding
                embedding = embedding / np.linalg.norm(embedding)

                # Get bounding box
                bbox = face.bbox.astype(int)
                left, top, right, bottom = bbox[0], bbox[1], bbox[2], bbox[3]

                name = "Unknown"
                user_id = None
                confidence = 0.0

                if len(self.known_encodings) > 0:
                    # Calculate cosine similarities with all known faces
                    similarities = []
                    for i, known_embedding in enumerate(self.known_encodings):
                        try:
                            # Check dimension compatibility
                            if len(embedding) != len(known_embedding):
                                logger.warning(f"⚠️ Dimension mismatch: InsightFace ({len(embedding)}D) vs stored ({len(known_embedding)}D) for {self.known_names[i]}")
                                # Skip this comparison - incompatible dimensions
                                similarities.append(0.0)
                                continue

                            # Cosine similarity using dot product (since embeddings are normalized)
                            similarity = np.dot(embedding, known_embedding)
                            similarities.append(similarity)
                        except Exception as sim_error:
                            logger.error(f"❌ Error comparing with {self.known_names[i]}: {sim_error}")
                            similarities.append(0.0)

                    similarities = np.array(similarities)
                    best_match_index = np.argmax(similarities)
                    best_similarity = similarities[best_match_index]

                    # Check if similarity is above threshold
                    if best_similarity >= self.similarity_threshold:
                        name = self.known_names[best_match_index]
                        user_id = self.known_user_ids[best_match_index]
                        confidence = best_similarity
                        self.successful_recognitions += 1
                        logger.info(f"👤 FACE RECOGNIZED: {name} (ID: {user_id}, similarity: {confidence:.3f})")

                recognition_time = time.time() - recognition_start
                self.recognition_times.append(recognition_time)

                results.append({
                    'name': name,
                    'user_id': user_id,
                    'confidence': confidence,
                    'location': (top, right, bottom, left),
                    'is_known': user_id is not None,
                    'detection_time': detection_time,
                    'recognition_time': recognition_time
                })

        except Exception as e:
            logger.error(f"❌ Error in InsightFace recognition: {e}")

        return results

    def _recognize_opencv(self, frame) -> List[Dict]:
        """Recognize faces using OpenCV + face_recognition fallback"""
        if not self.recognition_model:
            return []

        results = []

        try:
            start_time = time.time()

            # Resize frame for faster processing
            small_frame = cv2.resize(frame, (0, 0), fx=0.25, fy=0.25)
            rgb_small_frame = cv2.cvtColor(small_frame, cv2.COLOR_BGR2RGB)

            # Find faces and encodings
            face_locations = self.recognition_model.face_locations(rgb_small_frame, model="hog")
            face_encodings = self.recognition_model.face_encodings(rgb_small_frame, face_locations)

            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)
            self.total_detections += len(face_locations)

            if len(face_locations) > 0:
                logger.debug(f"🔍 OpenCV detected {len(face_locations)} face(s) in frame")

            for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
                recognition_start = time.time()

                # Scale back up face locations
                top *= 4
                right *= 4
                bottom *= 4
                left *= 4

                # Compare with known faces
                matches = self.recognition_model.compare_faces(
                    self.known_encodings, face_encoding, tolerance=0.6
                )
                distances = self.recognition_model.face_distance(self.known_encodings, face_encoding)

                name = "Unknown"
                user_id = None
                confidence = 0.0

                if True in matches:
                    best_match_index = np.argmin(distances)
                    if matches[best_match_index]:
                        name = self.known_names[best_match_index]
                        user_id = self.known_user_ids[best_match_index]
                        confidence = 1 - distances[best_match_index]
                        self.successful_recognitions += 1
                        logger.info(f"👤 FACE RECOGNIZED: {name} (ID: {user_id}, confidence: {confidence:.2f})")

                recognition_time = time.time() - recognition_start
                self.recognition_times.append(recognition_time)

                results.append({
                    'name': name,
                    'user_id': user_id,
                    'confidence': confidence,
                    'location': (top, right, bottom, left),
                    'is_known': user_id is not None,
                    'detection_time': detection_time,
                    'recognition_time': recognition_time
                })

        except Exception as e:
            logger.error(f"❌ Error in OpenCV recognition: {e}")

        return results

    def draw_face_boxes(self, frame, recognition_results: List[Dict]):
        """Draw bounding boxes and labels on frame"""
        for result in recognition_results:
            top, right, bottom, left = result['location']

            # Choose color and label based on recognition
            if result['is_known']:
                color = (0, 255, 0)  # Green for known faces
                confidence_percent = int(result['confidence'] * 100)
                label = f"{result['name']} ({confidence_percent}%)"
                status = "RECOGNIZED"
            else:
                color = (0, 0, 255)  # Red for unknown faces
                label = "Unknown Person"
                status = "UNKNOWN"

            # Draw main rectangle with thicker border
            cv2.rectangle(frame, (left, top), (right, bottom), color, 3)

            # Calculate label dimensions
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_DUPLEX, 0.7, 2)[0]
            status_size = cv2.getTextSize(status, cv2.FONT_HERSHEY_DUPLEX, 0.5, 1)[0]

            # Draw label background (larger for better visibility)
            label_height = max(label_size[1], status_size[1]) + 20
            cv2.rectangle(frame, (left, top - label_height), (right, top), color, cv2.FILLED)

            # Draw label text
            cv2.putText(frame, label, (left + 5, top - label_height + 15),
                       cv2.FONT_HERSHEY_DUPLEX, 0.7, (255, 255, 255), 2)

            # Draw status text
            cv2.putText(frame, status, (left + 5, top - 5),
                       cv2.FONT_HERSHEY_DUPLEX, 0.5, (255, 255, 255), 1)

        return frame

    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        avg_detection_time = np.mean(self.detection_times) if self.detection_times else 0
        avg_recognition_time = np.mean(self.recognition_times) if self.recognition_times else 0
        recognition_rate = (self.successful_recognitions / max(self.total_detections, 1)) * 100

        return {
            'total_detections': self.total_detections,
            'successful_recognitions': self.successful_recognitions,
            'recognition_rate': recognition_rate,
            'avg_detection_time': avg_detection_time,
            'avg_recognition_time': avg_recognition_time,
            'registered_faces': len(self.known_encodings),
            'similarity_threshold': self.similarity_threshold,
            'recognition_system': 'InsightFace ArcFace-R100' if self.use_insightface else 'OpenCV + face_recognition',
            'model_name': self.model_name
        }

    def reset_stats(self):
        """Reset performance statistics"""
        self.detection_times = []
        self.recognition_times = []
        self.total_detections = 0
        self.successful_recognitions = 0

# Global instance for the application
face_recognition_system = FaceRecognitionSystem()

# Legacy functions for backward compatibility
def extract_face_encoding(image_path: str) -> bytes:
    """Legacy function - extract face encoding using InsightFace"""
    return face_recognition_system.extract_face_encoding(image_path)

def recognize_face(known_encodings, frame):
    """Legacy function - use the new system instead"""
    logger.warning("Using legacy recognize_face function. Consider using face_recognition_system.recognize_faces_in_frame() instead.")
    # Use the new system for better performance
    results = face_recognition_system.recognize_faces_in_frame(frame)
    # Convert to old format for compatibility
    return [result['is_known'] for result in results]