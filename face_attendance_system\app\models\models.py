from sqlalchemy import Column, <PERSON>, Integer, DateTime, ForeignKey, LargeBinary, Boolean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base
import datetime

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    image_path = Column(String(500))
    face_encoding = Column(LargeBinary)  # Serialized face encoding
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    attendance_records = relationship("Attendance", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, name='{self.name}', email='{self.email}')>"

class Camera(Base):
    __tablename__ = "cameras"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    url = Column(String(500), nullable=False)  # RTSP URL or camera index
    type = Column(String(10), nullable=False)  # IN or OUT
    is_active = Column(Boolean, default=True)
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    attendance_records = relationship("Attendance", back_populates="camera")

    def __repr__(self):
        return f"<Camera(id={self.id}, name='{self.name}', type='{self.type}')>"

class Attendance(Base):
    __tablename__ = "attendance"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    camera_id = Column(Integer, ForeignKey('cameras.id'), nullable=False, index=True)
    date = Column(String(10), nullable=False, index=True)  # YYYY-MM-DD format
    login_time = Column(String(8))  # HH:MM:SS format
    logout_time = Column(String(8))  # HH:MM:SS format
    camera_name = Column(String(100))  # Denormalized for quick access
    camera_type = Column(String(10))   # Denormalized for quick access
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="attendance_records")
    camera = relationship("Camera", back_populates="attendance_records")

    def __repr__(self):
        return f"<Attendance(id={self.id}, user_id={self.user_id}, date='{self.date}')>"

    @property
    def duration_minutes(self):
        """Calculate duration in minutes"""
        if not self.login_time or not self.logout_time:
            return None

        try:
            login_dt = datetime.datetime.strptime(self.login_time, "%H:%M:%S")
            logout_dt = datetime.datetime.strptime(self.logout_time, "%H:%M:%S")
            duration = logout_dt - login_dt
            return int(duration.total_seconds() / 60)
        except:
            return None

    @property
    def duration_formatted(self):
        """Get formatted duration string"""
        minutes = self.duration_minutes
        if minutes is None:
            return None

        hours, mins = divmod(minutes, 60)
        return f"{hours:02d}:{mins:02d}"
