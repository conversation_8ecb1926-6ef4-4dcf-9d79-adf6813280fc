<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Test - Face Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .camera-test {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
        }
        .camera-stream {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-camera"></i> Face Attendance System - Camera Test
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link" href="/manage-camera">Camera Management</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>🔍 Camera Debugging Test</h1>
        <p class="text-muted">This page tests camera functionality without any face recognition or complex processing.</p>

        <!-- Camera Index Tests -->
        <div class="row">
            <div class="col-md-12">
                <div class="camera-test">
                    <h3>Step 1: Test Camera Indices</h3>
                    <p>Click each button to test different camera indices:</p>
                    
                    <div class="btn-group mb-3" role="group">
                        <button class="btn btn-primary" onclick="testCamera(0)">Test Camera 0</button>
                        <button class="btn btn-primary" onclick="testCamera(1)">Test Camera 1</button>
                        <button class="btn btn-primary" onclick="testCamera(2)">Test Camera 2</button>
                        <button class="btn btn-primary" onclick="testCamera(-1)">Test Camera -1</button>
                    </div>
                    
                    <div id="test-results"></div>
                </div>
            </div>
        </div>

        <!-- Camera Stream Test -->
        <div class="row">
            <div class="col-md-12">
                <div class="camera-test">
                    <h3>Step 2: Test Camera Stream</h3>
                    <p>Once you find a working camera index above, test the stream:</p>
                    
                    <div class="mb-3">
                        <label for="streamIndex" class="form-label">Camera Index:</label>
                        <input type="number" class="form-control" id="streamIndex" value="0" style="width: 100px; display: inline-block;">
                        <button class="btn btn-success ms-2" onclick="startStream()">Start Stream</button>
                        <button class="btn btn-danger ms-2" onclick="stopStream()">Stop Stream</button>
                    </div>
                    
                    <div id="stream-container">
                        <img id="camera-stream" class="camera-stream" style="display: none;" alt="Camera Stream">
                        <div id="stream-status" class="test-result info">Click "Start Stream" to test camera feed</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row">
            <div class="col-md-12">
                <div class="camera-test">
                    <h3>📋 Instructions</h3>
                    <ol>
                        <li><strong>Test Camera Indices:</strong> Click the test buttons to find which camera index works</li>
                        <li><strong>Check Results:</strong> Look for "success" status and note the working camera index</li>
                        <li><strong>Test Stream:</strong> Enter the working camera index and click "Start Stream"</li>
                        <li><strong>Verify Video:</strong> You should see live video feed from your webcam</li>
                        <li><strong>Use Working Index:</strong> Use the working camera index in Camera Management</li>
                    </ol>
                    
                    <div class="alert alert-info">
                        <strong>Common Issues:</strong>
                        <ul class="mb-0">
                            <li>Camera index 0 usually works for default webcam</li>
                            <li>Close other apps using webcam (Zoom, Teams, etc.)</li>
                            <li>Check webcam permissions in Windows settings</li>
                            <li>Try different camera indices if 0 doesn't work</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testCamera(index) {
            const resultsDiv = document.getElementById('test-results');
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = 'Testing...';
            button.disabled = true;
            
            fetch(`/simple-camera-test/${index}`)
                .then(response => response.json())
                .then(data => {
                    const resultClass = data.status === 'success' ? 'success' : 'error';
                    const resultHtml = `
                        <div class="test-result ${resultClass}">
                            <strong>Camera ${index}:</strong> ${data.status.toUpperCase()}<br>
                            ${data.status === 'success' ? 
                                `Resolution: ${data.resolution}, FPS: ${data.fps}, Shape: ${data.frame_shape}` : 
                                `Error: ${data.error}`
                            }
                        </div>
                    `;
                    resultsDiv.innerHTML += resultHtml;
                    
                    if (data.status === 'success') {
                        document.getElementById('streamIndex').value = index;
                    }
                })
                .catch(error => {
                    const resultHtml = `
                        <div class="test-result error">
                            <strong>Camera ${index}:</strong> ERROR<br>
                            Network error: ${error.message}
                        </div>
                    `;
                    resultsDiv.innerHTML += resultHtml;
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }
        
        function startStream() {
            const index = document.getElementById('streamIndex').value;
            const streamImg = document.getElementById('camera-stream');
            const statusDiv = document.getElementById('stream-status');
            
            statusDiv.innerHTML = `Starting stream for camera ${index}...`;
            statusDiv.className = 'test-result info';
            
            streamImg.src = `/minimal-camera-stream/${index}`;
            streamImg.style.display = 'block';
            
            streamImg.onload = function() {
                statusDiv.innerHTML = `✅ Camera ${index} stream is working!`;
                statusDiv.className = 'test-result success';
            };
            
            streamImg.onerror = function() {
                statusDiv.innerHTML = `❌ Failed to load camera ${index} stream`;
                statusDiv.className = 'test-result error';
                streamImg.style.display = 'none';
            };
        }
        
        function stopStream() {
            const streamImg = document.getElementById('camera-stream');
            const statusDiv = document.getElementById('stream-status');
            
            streamImg.src = '';
            streamImg.style.display = 'none';
            statusDiv.innerHTML = 'Stream stopped';
            statusDiv.className = 'test-result info';
        }
    </script>
</body>
</html>
