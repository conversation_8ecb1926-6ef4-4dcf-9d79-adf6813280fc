<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Logs - Face Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .filter-card {
            background-color: #f8f9fa;
        }
        .attendance-table {
            font-size: 0.9rem;
        }
        .duration-badge {
            min-width: 60px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-camera"></i> Face Attendance System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h1 class="mb-4">
                    <i class="fas fa-clock"></i> Attendance Logs
                </h1>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card filter-card">
                    <div class="card-body">
                        <form id="filterForm" class="row g-3">
                            <div class="col-md-3">
                                <label for="dateFilter" class="form-label">Date</label>
                                <input type="date" class="form-control" id="dateFilter" name="date_filter">
                            </div>
                            <div class="col-md-3">
                                <label for="userFilter" class="form-label">User</label>
                                <select class="form-select" id="userFilter" name="user_id">
                                    <option value="">All Users</option>
                                    {% for user in users %}
                                        <option value="{{ user.id }}">{{ user.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="cameraFilter" class="form-label">Camera Type</label>
                                <select class="form-select" id="cameraFilter" name="camera_type">
                                    <option value="">All Types</option>
                                    <option value="IN">IN (Entry)</option>
                                    <option value="OUT">OUT (Exit)</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Table -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> Attendance Records</h5>
                        <div>
                            <button class="btn btn-sm btn-success" onclick="exportData()">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="refreshData()">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="loadingSpinner" class="text-center d-none">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped attendance-table" id="attendanceTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Date</th>
                                        <th>Login Time</th>
                                        <th>Logout Time</th>
                                        <th>Duration</th>
                                        <th>Camera</th>
                                        <th>Type</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="attendanceTableBody">
                                    {% for log in logs %}
                                    <tr>
                                        <td>
                                            <a href="/user/{{ log.user_id }}" class="text-decoration-none">
                                                {{ log.name }}
                                            </a>
                                        </td>
                                        <td>{{ log.email }}</td>
                                        <td>{{ log.date }}</td>
                                        <td>{{ log.login_time or '-' }}</td>
                                        <td>{{ log.logout_time or '-' }}</td>
                                        <td>
                                            {% if log.duration %}
                                                <span class="badge bg-info duration-badge">{{ log.duration }}</span>
                                            {% else %}
                                                <span class="badge bg-warning duration-badge">Active</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ log.camera_name }}</td>
                                        <td>
                                            <span class="badge bg-{% if log.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                {{ log.camera_type }}
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="viewUserDetails({{ log.user_id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if not logs %}
                        <div class="text-center py-4">
                            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No attendance records found.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set today's date as default
        document.getElementById('dateFilter').value = new Date().toISOString().split('T')[0];

        // Filter form submission
        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            filterAttendance();
        });

        function filterAttendance() {
            const formData = new FormData(document.getElementById('filterForm'));
            const params = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                if (value) params.append(key, value);
            }

            showLoading(true);
            
            fetch(`/attendance/filter?${params}`)
                .then(response => response.json())
                .then(data => {
                    updateTable(data.logs);
                    showLoading(false);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error filtering attendance data');
                    showLoading(false);
                });
        }

        function updateTable(logs) {
            const tbody = document.getElementById('attendanceTableBody');
            tbody.innerHTML = '';

            if (logs.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="fas fa-clock fa-2x mb-2"></i><br>
                            No attendance records found for the selected filters.
                        </td>
                    </tr>
                `;
                return;
            }

            logs.forEach(log => {
                const row = `
                    <tr>
                        <td><a href="/user/${log.user_id}" class="text-decoration-none">${log.name}</a></td>
                        <td>${log.email}</td>
                        <td>${log.date}</td>
                        <td>${log.login_time || '-'}</td>
                        <td>${log.logout_time || '-'}</td>
                        <td>
                            ${log.duration ? 
                                `<span class="badge bg-info duration-badge">${log.duration}</span>` : 
                                `<span class="badge bg-warning duration-badge">Active</span>`
                            }
                        </td>
                        <td>${log.camera_name}</td>
                        <td>
                            <span class="badge bg-${log.camera_type === 'IN' ? 'success' : 'warning'}">
                                ${log.camera_type}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewUserDetails(${log.user_id})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        function showLoading(show) {
            const spinner = document.getElementById('loadingSpinner');
            const table = document.getElementById('attendanceTable');
            
            if (show) {
                spinner.classList.remove('d-none');
                table.style.opacity = '0.5';
            } else {
                spinner.classList.add('d-none');
                table.style.opacity = '1';
            }
        }

        function viewUserDetails(userId) {
            window.location.href = `/user/${userId}`;
        }

        function refreshData() {
            location.reload();
        }

        function exportData() {
            // Get current filter values
            const formData = new FormData(document.getElementById('filterForm'));
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value) params.append(key, value);
            }

            // Create download link
            const link = document.createElement('a');
            link.href = `/attendance/export-pdf?${params}`;
            link.download = 'attendance_report.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Auto-refresh every 60 seconds
        setInterval(refreshData, 60000);
    </script>
</body>
</html>
