#!/usr/bin/env python3
"""
Debug script to check face encoding format in database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.models import User
import numpy as np
import pickle
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_face_encodings():
    """Debug face encodings in database"""
    db = SessionLocal()
    try:
        # Get all users with face encodings
        users = db.query(User).filter(User.face_encoding.isnot(None)).all()
        
        logger.info(f"Found {len(users)} users with face encodings")
        
        for user in users:
            logger.info(f"\n--- User: {user.name} (ID: {user.id}) ---")
            logger.info(f"Face encoding type: {type(user.face_encoding)}")
            logger.info(f"Face encoding length: {len(user.face_encoding) if user.face_encoding else 0}")
            
            if user.face_encoding:
                # Try different decoding methods
                encoding = user.face_encoding
                
                # Method 1: Direct numpy frombuffer
                try:
                    if isinstance(encoding, bytes):
                        # Try float32 first (InsightFace format)
                        arr_f32 = np.frombuffer(encoding, dtype=np.float32)
                        logger.info(f"✅ Method 1 (float32): Shape {arr_f32.shape}, Range [{arr_f32.min():.3f}, {arr_f32.max():.3f}]")
                        
                        # Try float64
                        arr_f64 = np.frombuffer(encoding, dtype=np.float64)
                        logger.info(f"✅ Method 1 (float64): Shape {arr_f64.shape}, Range [{arr_f64.min():.3f}, {arr_f64.max():.3f}]")
                        
                except Exception as e:
                    logger.error(f"❌ Method 1 failed: {e}")
                
                # Method 2: Pickle loads
                try:
                    if isinstance(encoding, bytes):
                        arr_pickle = pickle.loads(encoding)
                        logger.info(f"✅ Method 2 (pickle): Type {type(arr_pickle)}, Shape {arr_pickle.shape if hasattr(arr_pickle, 'shape') else 'N/A'}")
                        if hasattr(arr_pickle, 'shape'):
                            logger.info(f"   Range [{arr_pickle.min():.3f}, {arr_pickle.max():.3f}]")
                except Exception as e:
                    logger.error(f"❌ Method 2 failed: {e}")
                
                # Method 3: Try to decode as string and eval
                try:
                    if isinstance(encoding, bytes):
                        str_encoding = encoding.decode('utf-8')
                        arr_eval = eval(str_encoding)
                        logger.info(f"✅ Method 3 (eval): Type {type(arr_eval)}, Shape {arr_eval.shape if hasattr(arr_eval, 'shape') else 'N/A'}")
                except Exception as e:
                    logger.error(f"❌ Method 3 failed: {e}")
                
                # Show first few bytes for debugging
                if isinstance(encoding, bytes):
                    logger.info(f"First 20 bytes: {encoding[:20]}")
                    logger.info(f"Last 20 bytes: {encoding[-20:]}")
    
    finally:
        db.close()

if __name__ == "__main__":
    debug_face_encodings()
