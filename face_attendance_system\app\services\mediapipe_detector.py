"""
MediaPipe Face Detection Module
Provides fast face detection using MediaPipe (< 5ms per frame)
"""

import cv2
import numpy as np
import mediapipe as mp
import logging
import time
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)

class MediaPipeFaceDetector:
    """
    Fast face detection using MediaPipe
    Optimized for CPU performance with <5ms detection time
    """
    
    def __init__(self, 
                 min_detection_confidence: float = 0.5,
                 model_selection: int = 0):
        """
        Initialize MediaPipe Face Detection
        
        Args:
            min_detection_confidence: Minimum confidence for face detection (0.0-1.0)
            model_selection: 0 for short-range model (2m), 1 for full-range model (5m)
        """
        self.min_detection_confidence = min_detection_confidence
        self.model_selection = model_selection
        
        # Initialize MediaPipe Face Detection
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Create face detection instance
        self.face_detection = self.mp_face_detection.FaceDetection(
            model_selection=model_selection,
            min_detection_confidence=min_detection_confidence
        )
        
        # Performance tracking
        self.detection_times = []
        self.total_detections = 0
        
        logger.info(f"✅ MediaPipe Face Detector initialized")
        logger.info(f"   Model: {'short-range (2m)' if model_selection == 0 else 'full-range (5m)'}")
        logger.info(f"   Min confidence: {min_detection_confidence}")
    
    def detect_faces(self, frame: np.ndarray) -> List[Tuple[int, int, int, int, float]]:
        """
        Detect faces in frame using MediaPipe
        
        Args:
            frame: Input BGR image from OpenCV
            
        Returns:
            List of tuples: (x, y, width, height, confidence)
        """
        if frame is None or frame.size == 0:
            return []
        
        start_time = time.time()
        
        try:
            # Convert BGR to RGB for MediaPipe
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process the frame
            results = self.face_detection.process(rgb_frame)
            
            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)
            
            faces = []
            
            if results.detections:
                self.total_detections += len(results.detections)
                
                height, width = frame.shape[:2]
                
                for detection in results.detections:
                    # Get bounding box
                    bbox = detection.location_data.relative_bounding_box
                    
                    # Convert relative coordinates to absolute
                    x = int(bbox.xmin * width)
                    y = int(bbox.ymin * height)
                    w = int(bbox.width * width)
                    h = int(bbox.height * height)
                    
                    # Ensure coordinates are within frame bounds
                    x = max(0, x)
                    y = max(0, y)
                    w = min(w, width - x)
                    h = min(h, height - y)
                    
                    # Get confidence score
                    confidence = detection.score[0] if detection.score else 0.0
                    
                    faces.append((x, y, w, h, confidence))
                
                logger.debug(f"🔍 MediaPipe detected {len(faces)} faces in {detection_time*1000:.1f}ms")
            
            return faces
            
        except Exception as e:
            logger.error(f"❌ MediaPipe detection error: {e}")
            return []
    
    def get_face_crops(self, frame: np.ndarray, 
                      faces: List[Tuple[int, int, int, int, float]],
                      target_size: Tuple[int, int] = (112, 112),
                      padding: float = 0.2) -> List[np.ndarray]:
        """
        Extract and resize face crops from detected faces
        
        Args:
            frame: Input BGR image
            faces: List of face detections (x, y, w, h, confidence)
            target_size: Target size for face crops (width, height)
            padding: Additional padding around face (0.0-1.0)
            
        Returns:
            List of cropped and resized face images
        """
        if frame is None or not faces:
            return []
        
        face_crops = []
        height, width = frame.shape[:2]
        
        for x, y, w, h, confidence in faces:
            try:
                # Add padding
                pad_w = int(w * padding)
                pad_h = int(h * padding)
                
                # Calculate expanded bounding box
                x1 = max(0, x - pad_w)
                y1 = max(0, y - pad_h)
                x2 = min(width, x + w + pad_w)
                y2 = min(height, y + h + pad_h)
                
                # Extract face crop
                face_crop = frame[y1:y2, x1:x2]
                
                if face_crop.size > 0:
                    # Resize to target size
                    face_resized = cv2.resize(face_crop, target_size, interpolation=cv2.INTER_LINEAR)
                    face_crops.append(face_resized)
                
            except Exception as e:
                logger.error(f"❌ Error cropping face: {e}")
                continue
        
        return face_crops
    
    def draw_detections(self, frame: np.ndarray, 
                       faces: List[Tuple[int, int, int, int, float]],
                       color: Tuple[int, int, int] = (0, 255, 0),
                       thickness: int = 2) -> np.ndarray:
        """
        Draw face detection boxes on frame
        
        Args:
            frame: Input BGR image
            faces: List of face detections
            color: Box color (B, G, R)
            thickness: Box line thickness
            
        Returns:
            Frame with detection boxes drawn
        """
        if frame is None:
            return frame
        
        result_frame = frame.copy()
        
        for x, y, w, h, confidence in faces:
            # Draw bounding box
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, thickness)
            
            # Draw confidence score
            label = f"{confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            cv2.rectangle(result_frame, (x, y - label_size[1] - 10), 
                         (x + label_size[0], y), color, -1)
            cv2.putText(result_frame, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return result_frame
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        if not self.detection_times:
            return {
                "avg_detection_time_ms": 0,
                "min_detection_time_ms": 0,
                "max_detection_time_ms": 0,
                "total_detections": 0,
                "total_frames_processed": 0
            }
        
        detection_times_ms = [t * 1000 for t in self.detection_times]
        
        return {
            "avg_detection_time_ms": np.mean(detection_times_ms),
            "min_detection_time_ms": np.min(detection_times_ms),
            "max_detection_time_ms": np.max(detection_times_ms),
            "total_detections": self.total_detections,
            "total_frames_processed": len(self.detection_times)
        }
    
    def __del__(self):
        """Cleanup MediaPipe resources"""
        try:
            if hasattr(self, 'face_detection'):
                self.face_detection.close()
        except:
            pass

# Global instance for the application
mediapipe_detector = None

def get_mediapipe_detector() -> MediaPipeFaceDetector:
    """Get or create global MediaPipe detector instance"""
    global mediapipe_detector
    if mediapipe_detector is None:
        mediapipe_detector = MediaPipeFaceDetector()
    return mediapipe_detector
