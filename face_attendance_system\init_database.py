#!/usr/bin/env python3
"""
Initialize the database with tables and sample users
"""
import os
import sys
import sqlite3
from datetime import datetime
import pickle
import numpy as np

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def create_database():
    """Create the SQLite database and tables"""
    db_path = 'app/database/attendance.db'
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Connect to database (creates file if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE,
            phone VARCHAR(20),
            face_encoding BLOB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create attendance table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            camera_id VARCHAR(50) NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            confidence REAL,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Create cameras table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cameras (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            rtsp_url TEXT NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    print("✅ Database tables created successfully")
    
    # Check if users already exist
    cursor.execute('SELECT COUNT(*) FROM users')
    user_count = cursor.fetchone()[0]
    
    if user_count == 0:
        print("📝 Adding sample users...")
        
        # Add sample users (without face encodings for now)
        sample_users = [
            ('John Doe', '<EMAIL>', '+1234567890'),
            ('Jane Smith', '<EMAIL>', '+1234567891'),
            ('Alice Johnson', '<EMAIL>', '+1234567892'),
            ('Bob Wilson', '<EMAIL>', '+1234567893'),
        ]
        
        for name, email, phone in sample_users:
            cursor.execute('''
                INSERT INTO users (name, email, phone)
                VALUES (?, ?, ?)
            ''', (name, email, phone))
        
        conn.commit()
        print(f"✅ Added {len(sample_users)} sample users")
    else:
        print(f"ℹ️ Database already has {user_count} users")
    
    # Add sample camera if none exist
    cursor.execute('SELECT COUNT(*) FROM cameras')
    camera_count = cursor.fetchone()[0]
    
    if camera_count == 0:
        cursor.execute('''
            INSERT INTO cameras (id, name, rtsp_url, is_active)
            VALUES (?, ?, ?, ?)
        ''', ('webcam', 'Default Webcam', '0', True))
        conn.commit()
        print("✅ Added default webcam camera")
    
    # Show current database status
    cursor.execute('SELECT id, name, face_encoding IS NOT NULL as has_face FROM users')
    users = cursor.fetchall()
    print(f"\n📊 Current users in database:")
    for user in users:
        print(f"  ID: {user[0]}, Name: {user[1]}, Has Face: {user[2]}")
    
    conn.close()
    print(f"\n✅ Database initialized at: {os.path.abspath(db_path)}")

if __name__ == "__main__":
    create_database()
